import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'MattverseFlow',
      fileName: (format) => `index.${format}.js`,
      formats: ['es', 'cjs'],
    },
    rollupOptions: {
      external: [
        'vue',
        'pinia',
        '@vue-flow/core',
        '@vue-flow/controls',
        '@vue-flow/minimap',
        '@vue-flow/background',
        '@vue-flow/node-resizer',
        '@vue-flow/node-toolbar',
        'zod',
        '@mattverse/shared',
      ],
      output: {
        globals: {
          vue: 'Vue',
          pinia: 'Pinia',
          '@vue-flow/core': 'VueFlowCore',
          '@vue-flow/controls': 'VueFlowControls',
          '@vue-flow/minimap': 'VueFlowMinimap',
          '@vue-flow/background': 'VueFlowBackground',
          '@vue-flow/node-resizer': 'VueFlowNodeResizer',
          '@vue-flow/node-toolbar': 'VueFlowNodeToolbar',
          zod: 'Zod',
        },
        assetFileNames: (assetInfo) => {
          if (assetInfo.name?.endsWith('.css')) {
            return 'style.css'
          }
          return assetInfo.name || 'assets/[name].[ext]'
        }
      },
    },
    cssCodeSplit: true,
    sourcemap: true,
  },
})
