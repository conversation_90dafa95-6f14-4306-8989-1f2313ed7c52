<template>
  <div class="node-palette">
    <div class="palette-header">
      <h3>节点面板</h3>
    </div>
    <div class="palette-content">
      <div class="node-category">
        <h4>基础节点</h4>
        <div class="node-list">
          <div
            v-for="nodeType in basicNodes"
            :key="nodeType.type"
            class="palette-node"
            :draggable="true"
            @dragstart="handleDragStart($event, nodeType)"
          >
            <div class="node-icon">{{ nodeType.icon }}</div>
            <div class="node-label">{{ nodeType.label }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface NodeType {
  type: string
  label: string
  icon: string
  description?: string
}

const basicNodes = ref<NodeType[]>([
  { type: 'start', label: '开始', icon: '▶️', description: '工作流开始节点' },
  { type: 'end', label: '结束', icon: '⏹️', description: '工作流结束节点' },
  { type: 'process', label: '处理', icon: '⚙️', description: '数据处理节点' },
  { type: 'decision', label: '判断', icon: '❓', description: '条件判断节点' },
])

const emit = defineEmits<{
  dragStart: [event: DragEvent, nodeType: NodeType]
}>()

const handleDragStart = (event: DragEvent, nodeType: NodeType) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify(nodeType))
    event.dataTransfer.effectAllowed = 'copy'
  }
  emit('dragStart', event, nodeType)
}
</script>

<style scoped>
.node-palette {
  width: 250px;
  background: white;
  border-right: 1px solid #e5e5e5;
  display: flex;
  flex-direction: column;
}

.palette-header {
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.palette-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.palette-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.node-category {
  margin-bottom: 24px;
}

.node-category h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.node-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.palette-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s;
  background: white;
}

.palette-node:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.palette-node:active {
  cursor: grabbing;
}

.node-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.node-label {
  font-size: 12px;
  color: #374151;
  text-align: center;
}
</style>
