<template>
  <div class="flow-canvas" ref="canvasRef">
    <!-- 背景网格 -->
    <div v-if="showBackground" class="flow-background">
      <svg class="background-pattern">
        <defs>
          <pattern id="grid" :width="gridSize" :height="gridSize" patternUnits="userSpaceOnUse">
            <path
              :d="`M ${gridSize} 0 L 0 0 0 ${gridSize}`"
              fill="none"
              :stroke="gridColor"
              stroke-width="1"
            />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
      </svg>
    </div>

    <!-- 节点容器 -->
    <div class="nodes-container" :style="containerStyle">
      <div
        v-for="node in nodes"
        :key="node.id"
        class="flow-node"
        :class="[`node-${node.type}`, { 'node-selected': selectedNodes.includes(node.id) }]"
        :style="getNodeStyle(node)"
        @click="handleNodeClick(node, $event)"
        @mousedown="handleNodeMouseDown(node, $event)"
      >
        <div class="node-content">
          <div class="node-header">
            <span class="node-title">{{ node.data?.label || node.id }}</span>
          </div>
          <div v-if="node.data?.description" class="node-description">
            {{ node.data.description }}
          </div>
        </div>

        <!-- 连接点 -->
        <div class="node-handles">
          <div class="handle handle-source" :data-node-id="node.id" data-handle-type="source"></div>
          <div class="handle handle-target" :data-node-id="node.id" data-handle-type="target"></div>
        </div>
      </div>
    </div>

    <!-- 边容器 -->
    <svg class="edges-container" :style="containerStyle">
      <g v-for="edge in edges" :key="edge.id">
        <path
          :d="getEdgePath(edge)"
          class="flow-edge"
          :class="{ 'edge-selected': selectedEdges.includes(edge.id) }"
          @click="handleEdgeClick(edge, $event)"
        />
        <!-- 边标签 -->
        <text
          v-if="edge.data?.label"
          :x="getEdgeLabelPosition(edge).x"
          :y="getEdgeLabelPosition(edge).y"
          class="edge-label"
          text-anchor="middle"
        >
          {{ edge.data.label }}
        </text>
      </g>
    </svg>

    <!-- 控制面板 -->
    <div v-if="showControls" class="flow-controls">
      <button @click="zoomIn" class="control-btn">+</button>
      <button @click="zoomOut" class="control-btn">-</button>
      <button @click="fitView" class="control-btn">⌂</button>
    </div>

    <!-- 小地图 -->
    <div v-if="showMiniMap" class="mini-map">
      <div class="mini-map-content">
        <!-- 简化的小地图实现 -->
        <div class="mini-viewport"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useFlowStore } from '../stores/flow'
import type { WorkflowNode, WorkflowEdge } from '../types'

// Props
interface Props {
  showBackground?: boolean
  showControls?: boolean
  showMiniMap?: boolean
  theme?: 'light' | 'dark'
}

const props = withDefaults(defineProps<Props>(), {
  showBackground: true,
  showControls: true,
  showMiniMap: false,
  theme: 'light',
})

// Emits
const emit = defineEmits<{
  nodeClick: [node: WorkflowNode, event: MouseEvent]
  edgeClick: [edge: WorkflowEdge, event: MouseEvent]
  paneClick: [event: MouseEvent]
  connect: [connection: any]
  drop: [event: DragEvent]
  dragover: [event: DragEvent]
}>()

// Store
const flowStore = useFlowStore()

// Refs
const canvasRef = ref<HTMLElement>()

// 计算属性
const nodes = computed(() => flowStore.nodes)
const edges = computed(() => flowStore.edges)
const selectedNodes = computed(() => flowStore.selectedNodes)
const selectedEdges = computed(() => flowStore.selectedEdges)
const viewport = computed(() => flowStore.viewport)

const gridSize = 20
const gridColor = '#e5e5e5'

const containerStyle = computed(() => ({
  transform: `translate(${viewport.value.x}px, ${viewport.value.y}px) scale(${viewport.value.zoom})`,
  transformOrigin: '0 0',
}))

// 方法
const getNodeStyle = (node: WorkflowNode) => ({
  left: `${node.position.x}px`,
  top: `${node.position.y}px`,
  width: node.width ? `${node.width}px` : '150px',
  height: node.height ? `${node.height}px` : '80px',
})

const getEdgePath = (edge: WorkflowEdge) => {
  const sourceNode = nodes.value.find(n => n.id === edge.source)
  const targetNode = nodes.value.find(n => n.id === edge.target)

  if (!sourceNode || !targetNode) return ''

  const sourceX = sourceNode.position.x + (sourceNode.width || 150) / 2
  const sourceY = sourceNode.position.y + (sourceNode.height || 80)
  const targetX = targetNode.position.x + (targetNode.width || 150) / 2
  const targetY = targetNode.position.y

  return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`
}

const getEdgeLabelPosition = (edge: WorkflowEdge) => {
  const sourceNode = nodes.value.find(n => n.id === edge.source)
  const targetNode = nodes.value.find(n => n.id === edge.target)

  if (!sourceNode || !targetNode) return { x: 0, y: 0 }

  const sourceX = sourceNode.position.x + (sourceNode.width || 150) / 2
  const sourceY = sourceNode.position.y + (sourceNode.height || 80)
  const targetX = targetNode.position.x + (targetNode.width || 150) / 2
  const targetY = targetNode.position.y

  return {
    x: (sourceX + targetX) / 2,
    y: (sourceY + targetY) / 2,
  }
}

// 事件处理
const handleNodeClick = (node: WorkflowNode, event: MouseEvent) => {
  event.stopPropagation()
  flowStore.selectNode(node.id, event.ctrlKey || event.metaKey)
  emit('nodeClick', node, event)
}

const handleEdgeClick = (edge: WorkflowEdge, event: MouseEvent) => {
  event.stopPropagation()
  flowStore.selectEdge(edge.id, event.ctrlKey || event.metaKey)
  emit('edgeClick', edge, event)
}

const handleNodeMouseDown = (node: WorkflowNode, event: MouseEvent) => {
  // 实现节点拖拽逻辑
  let isDragging = false
  const startX = event.clientX
  const startY = event.clientY
  const startNodeX = node.position.x
  const startNodeY = node.position.y

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) {
      isDragging = true
    }

    const deltaX = (e.clientX - startX) / viewport.value.zoom
    const deltaY = (e.clientY - startY) / viewport.value.zoom

    flowStore.updateNode(node.id, {
      position: {
        x: startNodeX + deltaX,
        y: startNodeY + deltaY,
      },
    })
  }

  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const handlePaneClick = (event: MouseEvent) => {
  if (event.target === canvasRef.value) {
    flowStore.clearSelection()
    emit('paneClick', event)
  }
}

// 缩放控制
const zoomIn = () => {
  const newZoom = Math.min(viewport.value.zoom * 1.2, 2)
  flowStore.setViewport({ ...viewport.value, zoom: newZoom })
}

const zoomOut = () => {
  const newZoom = Math.max(viewport.value.zoom / 1.2, 0.1)
  flowStore.setViewport({ ...viewport.value, zoom: newZoom })
}

const fitView = () => {
  // 简单的适应视图实现
  flowStore.setViewport({ x: 0, y: 0, zoom: 1 })
}

// 拖放处理
const handleDrop = (event: DragEvent) => {
  emit('drop', event)
}

const handleDragOver = (event: DragEvent) => {
  emit('dragover', event)
}

// 生命周期
onMounted(() => {
  if (canvasRef.value) {
    canvasRef.value.addEventListener('click', handlePaneClick)
    canvasRef.value.addEventListener('drop', handleDrop)
    canvasRef.value.addEventListener('dragover', handleDragOver)
  }
})

onUnmounted(() => {
  if (canvasRef.value) {
    canvasRef.value.removeEventListener('click', handlePaneClick)
    canvasRef.value.removeEventListener('drop', handleDrop)
    canvasRef.value.removeEventListener('dragover', handleDragOver)
  }
})
</script>

<style scoped>
.flow-canvas {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #fafafa;
}

.flow-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.background-pattern {
  width: 100%;
  height: 100%;
}

.nodes-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.flow-node {
  position: absolute;
  background: white;
  border: 2px solid #e5e5e5;
  border-radius: 8px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s;
}

.flow-node:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.flow-node.node-selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.node-content {
  padding: 8px 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.node-header {
  font-weight: 600;
  font-size: 14px;
  color: #374151;
}

.node-description {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  flex: 1;
}

.node-handles {
  position: absolute;
}

.handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border: 2px solid white;
  border-radius: 50%;
  cursor: crosshair;
}

.handle-source {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.handle-target {
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.edges-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.flow-edge {
  fill: none;
  stroke: #6b7280;
  stroke-width: 2;
  pointer-events: stroke;
  cursor: pointer;
}

.flow-edge:hover {
  stroke: #3b82f6;
  stroke-width: 3;
}

.flow-edge.edge-selected {
  stroke: #3b82f6;
  stroke-width: 3;
}

.edge-label {
  font-size: 12px;
  fill: #374151;
  pointer-events: none;
}

.flow-controls {
  position: absolute;
  bottom: 20px;
  left: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-btn {
  width: 32px;
  height: 32px;
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: all 0.2s;
}

.control-btn:hover {
  background: #f3f4f6;
  border-color: #3b82f6;
}

.mini-map {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 200px;
  height: 150px;
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  overflow: hidden;
}

.mini-map-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.mini-viewport {
  position: absolute;
  border: 2px solid #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

/* 节点类型样式 */
.node-start {
  border-color: #10b981;
}

.node-start.node-selected {
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.node-end {
  border-color: #ef4444;
}

.node-end.node-selected {
  border-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.node-process {
  border-color: #3b82f6;
}

.node-decision {
  border-color: #f59e0b;
  transform: rotate(45deg);
}

.node-decision .node-content {
  transform: rotate(-45deg);
}
</style>
