<template>
  <div class="property-panel">
    <div class="panel-header">
      <h3>属性面板</h3>
    </div>
    <div class="panel-content">
      <div v-if="!selectedNode && !selectedEdge" class="no-selection">
        <p>请选择一个节点或边来编辑属性</p>
      </div>

      <!-- 节点属性 -->
      <div v-if="selectedNode" class="property-section">
        <h4>节点属性</h4>
        <div class="property-group">
          <label>节点ID</label>
          <input v-model="selectedNode.id" type="text" readonly />
        </div>
        <div class="property-group">
          <label>节点类型</label>
          <select v-model="selectedNode.type" @change="handleNodeTypeChange">
            <option value="start">开始</option>
            <option value="end">结束</option>
            <option value="process">处理</option>
            <option value="decision">判断</option>
          </select>
        </div>
        <div class="property-group">
          <label>标题</label>
          <input
            v-model="nodeLabel"
            type="text"
            placeholder="输入节点标题"
            @input="handleLabelChange"
          />
        </div>
        <div class="property-group">
          <label>描述</label>
          <textarea
            v-model="nodeDescription"
            placeholder="输入节点描述"
            @input="handleDescriptionChange"
          ></textarea>
        </div>
        <div class="property-group">
          <label>位置</label>
          <div class="position-inputs">
            <input
              v-model.number="selectedNode.position.x"
              type="number"
              placeholder="X"
              @input="handlePositionChange"
            />
            <input
              v-model.number="selectedNode.position.y"
              type="number"
              placeholder="Y"
              @input="handlePositionChange"
            />
          </div>
        </div>
        <div class="property-group">
          <label>尺寸</label>
          <div class="size-inputs">
            <input
              v-model.number="selectedNode.width"
              type="number"
              placeholder="宽度"
              @input="handleSizeChange"
            />
            <input
              v-model.number="selectedNode.height"
              type="number"
              placeholder="高度"
              @input="handleSizeChange"
            />
          </div>
        </div>
      </div>

      <!-- 边属性 -->
      <div v-if="selectedEdge" class="property-section">
        <h4>边属性</h4>
        <div class="property-group">
          <label>边ID</label>
          <input v-model="selectedEdge.id" type="text" readonly />
        </div>
        <div class="property-group">
          <label>源节点</label>
          <input v-model="selectedEdge.source" type="text" readonly />
        </div>
        <div class="property-group">
          <label>目标节点</label>
          <input v-model="selectedEdge.target" type="text" readonly />
        </div>
        <div class="property-group">
          <label>标签</label>
          <input
            v-model="edgeLabel"
            type="text"
            placeholder="输入边标签"
            @input="handleEdgeLabelChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { useFlowStore } from '../stores/flow'
import type { WorkflowNode, WorkflowEdge } from '../types'

const flowStore = useFlowStore()

// 计算属性
const selectedNode = computed(() => {
  const selectedIds = flowStore.selectedNodes
  if (selectedIds.length === 1) {
    return flowStore.nodes.find(n => n.id === selectedIds[0])
  }
  return null
})

const selectedEdge = computed(() => {
  const selectedIds = flowStore.selectedEdges
  if (selectedIds.length === 1) {
    return flowStore.edges.find(e => e.id === selectedIds[0])
  }
  return null
})

const nodeLabel = computed({
  get: () => selectedNode.value?.data?.label || '',
  set: (value: string) => {
    if (selectedNode.value) {
      handleLabelChange({ target: { value } } as any)
    }
  },
})

const nodeDescription = computed({
  get: () => selectedNode.value?.data?.description || '',
  set: (value: string) => {
    if (selectedNode.value) {
      handleDescriptionChange({ target: { value } } as any)
    }
  },
})

const edgeLabel = computed({
  get: () => selectedEdge.value?.data?.label || '',
  set: (value: string) => {
    if (selectedEdge.value) {
      handleEdgeLabelChange({ target: { value } } as any)
    }
  },
})

// 事件处理
const handleNodeTypeChange = () => {
  if (selectedNode.value) {
    flowStore.updateNode(selectedNode.value.id, {
      type: selectedNode.value.type,
    })
  }
}

const handleLabelChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (selectedNode.value) {
    flowStore.updateNode(selectedNode.value.id, {
      data: {
        ...selectedNode.value.data,
        label: target.value,
      },
    })
  }
}

const handleDescriptionChange = (event: Event) => {
  const target = event.target as HTMLTextAreaElement
  if (selectedNode.value) {
    flowStore.updateNode(selectedNode.value.id, {
      data: {
        ...selectedNode.value.data,
        description: target.value,
      },
    })
  }
}

const handlePositionChange = () => {
  if (selectedNode.value) {
    flowStore.updateNode(selectedNode.value.id, {
      position: selectedNode.value.position,
    })
  }
}

const handleSizeChange = () => {
  if (selectedNode.value) {
    flowStore.updateNode(selectedNode.value.id, {
      width: selectedNode.value.width,
      height: selectedNode.value.height,
    })
  }
}

const handleEdgeLabelChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (selectedEdge.value) {
    flowStore.updateEdge(selectedEdge.value.id, {
      data: {
        ...selectedEdge.value.data,
        label: target.value,
      },
    })
  }
}
</script>

<style scoped>
.property-panel {
  width: 300px;
  background: white;
  border-left: 1px solid #e5e5e5;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.no-selection {
  text-align: center;
  color: #6b7280;
  margin-top: 40px;
}

.property-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.property-group {
  margin-bottom: 16px;
}

.property-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

.property-group input,
.property-group select,
.property-group textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.property-group input:focus,
.property-group select:focus,
.property-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.property-group input[readonly] {
  background: #f9fafb;
  color: #6b7280;
}

.property-group textarea {
  resize: vertical;
  min-height: 60px;
}

.position-inputs,
.size-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}
</style>
