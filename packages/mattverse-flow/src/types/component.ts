/**
 * 组件相关类型定义
 */
import type { Viewport } from './base'

/**
 * 流程画布组件属性
 */
export interface FlowCanvasProps {
  // 显示选项
  showBackground?: boolean
  showControls?: boolean
  showMiniMap?: boolean

  // 交互选项
  snapToGrid?: boolean
  snapGrid?: [number, number]
  fitViewOnInit?: boolean

  // 缩放选项
  minZoom?: number
  maxZoom?: number
  defaultViewport?: Viewport

  // 背景选项
  backgroundPattern?: 'dots' | 'lines' | 'cross'
  backgroundGap?: number
  backgroundSize?: number
  backgroundColor?: string

  // 控制器选项
  showZoom?: boolean
  showFitView?: boolean
  showInteractive?: boolean

  // 小地图选项
  miniMapNodeColor?: string
  miniMapNodeStrokeColor?: string
  miniMapNodeBorderRadius?: number
  miniMapMaskColor?: string

  // 连接选项
  connectionMode?: 'strict' | 'loose'
}
