/**
 * 执行相关类型定义
 */

/**
 * 工作流执行上下文
 */
export interface WorkflowExecutionContext {
  variables?: Record<string, any>
  settings?: Record<string, any>
  userId?: string
  sessionId?: string
  [key: string]: any
}

/**
 * 工作流执行结果
 */
export interface WorkflowExecutionResult {
  success: boolean
  workflowId: string
  executionId: string
  startTime: Date
  endTime?: Date
  duration?: number
  result?: any
  error?: string
  logs: WorkflowExecutionLog[]
}

/**
 * 工作流执行日志
 */
export interface WorkflowExecutionLog {
  id: string
  nodeId?: string
  level: 'info' | 'warn' | 'error' | 'debug'
  message: string
  timestamp: Date
  data?: any
}
