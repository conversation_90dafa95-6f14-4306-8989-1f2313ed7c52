/**
 * 工作流相关类型定义
 */
import type { WorkflowNode } from './node'
import type { WorkflowEdge } from './edge'

/**
 * 工作流验证结果
 */
export interface WorkflowValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

/**
 * 工作流状态
 */
export type WorkflowStatus = 'draft' | 'published' | 'archived'

/**
 * 工作流元数据
 */
export interface WorkflowMetadata {
  author?: string
  category?: string
  complexity?: 'simple' | 'medium' | 'complex'
  estimatedDuration?: number
  requiredResources?: string[]
  [key: string]: any
}

/**
 * 工作流接口
 */
export interface Workflow {
  id: string
  name: string
  description?: string
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
  version: string
  status: WorkflowStatus
  tags: string[]
  metadata: WorkflowMetadata
}
