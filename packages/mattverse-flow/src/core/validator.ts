/**
 * 工作流验证器
 */
import type { Workflow, WorkflowValidationResult } from '../types'

export class WorkflowValidator {
  /**
   * 验证工作流
   */
  validate(workflow: Workflow): WorkflowValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // 基础验证
    this.validateBasic(workflow, errors)

    // 节点验证
    this.validateNodes(workflow, errors, warnings)

    // 边验证
    this.validateEdges(workflow, errors, warnings)

    // 连接验证
    this.validateConnections(workflow, errors, warnings)

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    }
  }

  /**
   * 基础验证
   */
  private validateBasic(workflow: Workflow, errors: string[]) {
    if (!workflow.id) {
      errors.push('Workflow ID is required')
    }

    if (!workflow.name || workflow.name.trim() === '') {
      errors.push('Workflow name is required')
    }

    if (!Array.isArray(workflow.nodes)) {
      errors.push('Workflow nodes must be an array')
    }

    if (!Array.isArray(workflow.edges)) {
      errors.push('Workflow edges must be an array')
    }
  }

  /**
   * 节点验证
   */
  private validateNodes(workflow: Workflow, errors: string[], warnings: string[]) {
    if (workflow.nodes.length === 0) {
      errors.push('Workflow must have at least one node')
      return
    }

    const nodeIds = new Set<string>()
    const startNodes = workflow.nodes.filter(node => node.type === 'start')
    const endNodes = workflow.nodes.filter(node => node.type === 'end')

    // 检查开始节点
    if (startNodes.length === 0) {
      errors.push('Workflow must have at least one start node')
    } else if (startNodes.length > 1) {
      warnings.push('Workflow has multiple start nodes')
    }

    // 检查结束节点
    if (endNodes.length === 0) {
      warnings.push('Workflow should have at least one end node')
    }

    // 检查节点唯一性和必需字段
    for (const node of workflow.nodes) {
      if (!node.id) {
        errors.push('All nodes must have an ID')
        continue
      }

      if (nodeIds.has(node.id)) {
        errors.push(`Duplicate node ID: ${node.id}`)
      }
      nodeIds.add(node.id)

      if (!node.type) {
        errors.push(`Node ${node.id} must have a type`)
      }

      if (!node.position) {
        errors.push(`Node ${node.id} must have a position`)
      }
    }
  }

  /**
   * 边验证
   */
  private validateEdges(workflow: Workflow, errors: string[], warnings: string[]) {
    const edgeIds = new Set<string>()
    const nodeIds = new Set(workflow.nodes.map(node => node.id))

    for (const edge of workflow.edges) {
      if (!edge.id) {
        errors.push('All edges must have an ID')
        continue
      }

      if (edgeIds.has(edge.id)) {
        errors.push(`Duplicate edge ID: ${edge.id}`)
      }
      edgeIds.add(edge.id)

      if (!edge.source) {
        errors.push(`Edge ${edge.id} must have a source`)
      } else if (!nodeIds.has(edge.source)) {
        errors.push(`Edge ${edge.id} references non-existent source node: ${edge.source}`)
      }

      if (!edge.target) {
        errors.push(`Edge ${edge.id} must have a target`)
      } else if (!nodeIds.has(edge.target)) {
        errors.push(`Edge ${edge.id} references non-existent target node: ${edge.target}`)
      }

      if (edge.source === edge.target) {
        warnings.push(`Edge ${edge.id} creates a self-loop`)
      }
    }
  }

  /**
   * 连接验证
   */
  private validateConnections(workflow: Workflow, errors: string[], warnings: string[]) {
    const nodeConnections = new Map<string, { incoming: number; outgoing: number }>()

    // 初始化连接计数
    for (const node of workflow.nodes) {
      nodeConnections.set(node.id, { incoming: 0, outgoing: 0 })
    }

    // 计算连接数
    for (const edge of workflow.edges) {
      const source = nodeConnections.get(edge.source)
      const target = nodeConnections.get(edge.target)

      if (source) source.outgoing++
      if (target) target.incoming++
    }

    // 检查连接问题
    for (const [nodeId, connections] of nodeConnections) {
      const node = workflow.nodes.find(n => n.id === nodeId)
      if (!node) continue

      // 开始节点不应该有输入连接
      if (node.type === 'start' && connections.incoming > 0) {
        warnings.push(`Start node ${nodeId} has incoming connections`)
      }

      // 结束节点不应该有输出连接
      if (node.type === 'end' && connections.outgoing > 0) {
        warnings.push(`End node ${nodeId} has outgoing connections`)
      }

      // 孤立节点检查
      if (connections.incoming === 0 && connections.outgoing === 0 && node.type !== 'start') {
        warnings.push(`Node ${nodeId} is isolated (no connections)`)
      }

      // 死胡同节点检查
      if (connections.outgoing === 0 && node.type !== 'end') {
        warnings.push(`Node ${nodeId} is a dead end (no outgoing connections)`)
      }
    }

    // 检查可达性
    this.validateReachability(workflow, errors, warnings)
  }

  /**
   * 验证可达性
   */
  private validateReachability(workflow: Workflow, errors: string[], warnings: string[]) {
    const startNodes = workflow.nodes.filter(node => node.type === 'start')
    if (startNodes.length === 0) return

    const reachableNodes = new Set<string>()
    const queue = [...startNodes.map(node => node.id)]

    // BFS 遍历找到所有可达节点
    while (queue.length > 0) {
      const currentId = queue.shift()!
      if (reachableNodes.has(currentId)) continue

      reachableNodes.add(currentId)

      // 找到所有出边
      const outgoingEdges = workflow.edges.filter(edge => edge.source === currentId)
      for (const edge of outgoingEdges) {
        if (!reachableNodes.has(edge.target)) {
          queue.push(edge.target)
        }
      }
    }

    // 检查不可达节点
    for (const node of workflow.nodes) {
      if (!reachableNodes.has(node.id) && node.type !== 'start') {
        warnings.push(`Node ${node.id} is not reachable from start nodes`)
      }
    }
  }
}
