/**
 * 工作流执行器
 */
import { generateId } from '@mattverse/shared'
import type {
  WorkflowExecutionContext,
  WorkflowExecutionResult,
  WorkflowExecutionLog,
} from '../types'

export class WorkflowExecutor {
  private executions = new Map<string, WorkflowExecutionResult>()
  private runningExecutions = new Set<string>()

  /**
   * 执行工作流
   */
  async execute(
    workflowId: string,
    context: WorkflowExecutionContext = {}
  ): Promise<WorkflowExecutionResult> {
    const executionId = generateId()
    const startTime = new Date()

    const execution: WorkflowExecutionResult = {
      success: false,
      workflowId,
      executionId,
      startTime,
      logs: [],
    }

    this.executions.set(executionId, execution)
    this.runningExecutions.add(executionId)

    try {
      this.addLog(executionId, 'info', 'Starting workflow execution')

      // 模拟工作流执行
      await this.simulateExecution(executionId, context)

      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      const completedExecution: WorkflowExecutionResult = {
        ...execution,
        success: true,
        endTime,
        duration,
        result: { message: 'Workflow completed successfully' },
      }

      this.executions.set(executionId, completedExecution)
      this.addLog(executionId, 'info', `Workflow completed in ${duration}ms`)

      return completedExecution
    } catch (error) {
      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'

      const failedExecution: WorkflowExecutionResult = {
        ...execution,
        success: false,
        endTime,
        duration,
        error: errorMessage,
      }

      this.executions.set(executionId, failedExecution)
      this.addLog(executionId, 'error', `Workflow failed: ${errorMessage}`)

      return failedExecution
    } finally {
      this.runningExecutions.delete(executionId)
    }
  }

  /**
   * 停止执行
   */
  stopExecution(executionId: string): boolean {
    if (this.runningExecutions.has(executionId)) {
      this.runningExecutions.delete(executionId)
      this.addLog(executionId, 'warn', 'Execution stopped by user')
      return true
    }
    return false
  }

  /**
   * 获取执行状态
   */
  getExecutionStatus(executionId: string) {
    return this.executions.get(executionId)
  }

  /**
   * 模拟工作流执行
   */
  private async simulateExecution(executionId: string, context: WorkflowExecutionContext) {
    // 模拟执行步骤
    const steps = [
      'Initializing workflow',
      'Processing input data',
      'Executing business logic',
      'Generating output',
      'Finalizing results',
    ]

    for (let i = 0; i < steps.length; i++) {
      if (!this.runningExecutions.has(executionId)) {
        throw new Error('Execution was stopped')
      }

      this.addLog(executionId, 'info', steps[i])

      // 模拟处理时间
      await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))
    }
  }

  /**
   * 添加执行日志
   */
  private addLog(
    executionId: string,
    level: 'info' | 'warn' | 'error' | 'debug',
    message: string,
    data?: any
  ) {
    const execution = this.executions.get(executionId)
    if (execution) {
      const log: WorkflowExecutionLog = {
        id: generateId(),
        level,
        message,
        timestamp: new Date(),
        data,
      }

      execution.logs.push(log)
      this.executions.set(executionId, execution)
    }
  }
}
