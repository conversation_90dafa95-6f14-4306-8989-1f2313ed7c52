/**
 * 工作流常量定义
 */

/**
 * 默认节点尺寸
 */
export const DEFAULT_NODE_SIZE = {
  width: 150,
  height: 80,
}

/**
 * 节点类型配置
 */
export const NODE_TYPES = {
  START: 'start',
  END: 'end',
  PROCESS: 'process',
  DECISION: 'decision',
  CUSTOM: 'custom',
} as const

/**
 * 边类型配置
 */
export const EDGE_TYPES = {
  DEFAULT: 'default',
  STRAIGHT: 'straight',
  BEZIER: 'bezier',
} as const

/**
 * 默认主题配置
 */
export const DEFAULT_THEME = {
  light: {
    background: '#ffffff',
    nodeBg: '#ffffff',
    nodeBorder: '#e2e8f0',
    edgeColor: '#64748b',
    textColor: '#1e293b',
  },
  dark: {
    background: '#1e293b',
    nodeBg: '#334155',
    nodeBorder: '#475569',
    edgeColor: '#94a3b8',
    textColor: '#f1f5f9',
  },
}

/**
 * 网格配置
 */
export const GRID_CONFIG = {
  size: 20,
  color: '#e2e8f0',
  pattern: 'dots',
}

/**
 * 缩放配置
 */
export const ZOOM_CONFIG = {
  min: 0.1,
  max: 2,
  step: 0.1,
  default: 1,
}
