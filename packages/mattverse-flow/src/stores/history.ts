/**
 * 工作流历史记录状态管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { WorkflowNode, WorkflowEdge } from '../types'

export interface HistoryAction {
  id: string
  type:
    | 'add-node'
    | 'remove-node'
    | 'update-node'
    | 'add-edge'
    | 'remove-edge'
    | 'update-edge'
    | 'batch'
  timestamp: Date
  data: any
  description: string
}

export interface HistoryState {
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
}

export const useHistoryStore = defineStore('mattverse-history', () => {
  // 状态
  const history = ref<HistoryAction[]>([])
  const currentIndex = ref(-1)
  const maxHistorySize = ref(50)

  // 计算属性
  const canUndo = computed(() => currentIndex.value >= 0)
  const canRedo = computed(() => currentIndex.value < history.value.length - 1)

  const currentAction = computed(() =>
    currentIndex.value >= 0 ? history.value[currentIndex.value] : null
  )

  // 历史记录操作
  const addAction = (action: Omit<HistoryAction, 'id' | 'timestamp'>) => {
    const newAction: HistoryAction = {
      ...action,
      id: `action-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
    }

    // 如果当前不在历史记录的末尾，删除后面的记录
    if (currentIndex.value < history.value.length - 1) {
      history.value = history.value.slice(0, currentIndex.value + 1)
    }

    // 添加新动作
    history.value.push(newAction)
    currentIndex.value = history.value.length - 1

    // 限制历史记录大小
    if (history.value.length > maxHistorySize.value) {
      const removeCount = history.value.length - maxHistorySize.value
      history.value = history.value.slice(removeCount)
      currentIndex.value -= removeCount
    }
  }

  const undo = (): HistoryAction | null => {
    if (!canUndo.value) return null

    const action = history.value[currentIndex.value]
    currentIndex.value--
    return action
  }

  const redo = (): HistoryAction | null => {
    if (!canRedo.value) return null

    currentIndex.value++
    const action = history.value[currentIndex.value]
    return action
  }

  // 批量操作
  const startBatch = () => {
    // 可以用于标记批量操作的开始
  }

  const endBatch = (description: string, batchData: any) => {
    addAction({
      type: 'batch',
      description,
      data: batchData,
    })
  }

  // 快照操作
  const createSnapshot = (nodes: WorkflowNode[], edges: WorkflowEdge[], description: string) => {
    addAction({
      type: 'batch',
      description: `Snapshot: ${description}`,
      data: {
        nodes: JSON.parse(JSON.stringify(nodes)),
        edges: JSON.parse(JSON.stringify(edges)),
      },
    })
  }

  // 节点操作记录
  const recordNodeAdd = (node: WorkflowNode) => {
    addAction({
      type: 'add-node',
      description: `Added node: ${node.data?.label || node.id}`,
      data: { node: JSON.parse(JSON.stringify(node)) },
    })
  }

  const recordNodeRemove = (node: WorkflowNode) => {
    addAction({
      type: 'remove-node',
      description: `Removed node: ${node.data?.label || node.id}`,
      data: { node: JSON.parse(JSON.stringify(node)) },
    })
  }

  const recordNodeUpdate = (oldNode: WorkflowNode, newNode: WorkflowNode) => {
    addAction({
      type: 'update-node',
      description: `Updated node: ${newNode.data?.label || newNode.id}`,
      data: {
        oldNode: JSON.parse(JSON.stringify(oldNode)),
        newNode: JSON.parse(JSON.stringify(newNode)),
      },
    })
  }

  // 边操作记录
  const recordEdgeAdd = (edge: WorkflowEdge) => {
    addAction({
      type: 'add-edge',
      description: `Added edge: ${edge.source} → ${edge.target}`,
      data: { edge: JSON.parse(JSON.stringify(edge)) },
    })
  }

  const recordEdgeRemove = (edge: WorkflowEdge) => {
    addAction({
      type: 'remove-edge',
      description: `Removed edge: ${edge.source} → ${edge.target}`,
      data: { edge: JSON.parse(JSON.stringify(edge)) },
    })
  }

  const recordEdgeUpdate = (oldEdge: WorkflowEdge, newEdge: WorkflowEdge) => {
    addAction({
      type: 'update-edge',
      description: `Updated edge: ${newEdge.source} → ${newEdge.target}`,
      data: {
        oldEdge: JSON.parse(JSON.stringify(oldEdge)),
        newEdge: JSON.parse(JSON.stringify(newEdge)),
      },
    })
  }

  // 获取历史记录
  const getHistory = () => {
    return history.value.slice()
  }

  const getRecentActions = (count = 10) => {
    return history.value.slice(-count)
  }

  // 清理操作
  const clear = () => {
    history.value = []
    currentIndex.value = -1
  }

  const clearOldHistory = (keepCount = 20) => {
    if (history.value.length > keepCount) {
      const removeCount = history.value.length - keepCount
      history.value = history.value.slice(removeCount)
      currentIndex.value = Math.max(-1, currentIndex.value - removeCount)
    }
  }

  // 设置配置
  const setMaxHistorySize = (size: number) => {
    maxHistorySize.value = Math.max(1, size)

    // 如果当前历史记录超过新的限制，进行裁剪
    if (history.value.length > maxHistorySize.value) {
      const removeCount = history.value.length - maxHistorySize.value
      history.value = history.value.slice(removeCount)
      currentIndex.value = Math.max(-1, currentIndex.value - removeCount)
    }
  }

  // 重置状态
  const $reset = () => {
    history.value = []
    currentIndex.value = -1
    maxHistorySize.value = 50
  }

  return {
    // 状态
    history,
    currentIndex,
    maxHistorySize,

    // 计算属性
    canUndo,
    canRedo,
    currentAction,

    // 基础操作
    addAction,
    undo,
    redo,

    // 批量操作
    startBatch,
    endBatch,
    createSnapshot,

    // 节点操作记录
    recordNodeAdd,
    recordNodeRemove,
    recordNodeUpdate,

    // 边操作记录
    recordEdgeAdd,
    recordEdgeRemove,
    recordEdgeUpdate,

    // 查询操作
    getHistory,
    getRecentActions,

    // 清理操作
    clear,
    clearOldHistory,
    setMaxHistorySize,

    // 重置
    $reset,
  }
})
