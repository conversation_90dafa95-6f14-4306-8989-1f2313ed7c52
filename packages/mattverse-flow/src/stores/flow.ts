/**
 * 工作流画布状态管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  WorkflowNode,
  WorkflowEdge,
  FlowState,
  NodeChange,
  EdgeChange,
  Connection,
  Viewport,
  EdgeType,
} from '../types'
import { generateId } from '@mattverse/shared'

export const useFlowStore = defineStore('mattverse-flow', () => {
  // 状态
  const nodes = ref<WorkflowNode[]>([])
  const edges = ref<WorkflowEdge[]>([])
  const selectedNodes = ref<string[]>([])
  const selectedEdges = ref<string[]>([])
  const viewport = ref<Viewport>({ x: 0, y: 0, zoom: 1 })
  const isLoading = ref(false)
  const isDirty = ref(false)

  // 计算属性
  const selectedElements = computed(() => ({
    nodes: nodes.value.filter(node => selectedNodes.value.includes(node.id)),
    edges: edges.value.filter(edge => selectedEdges.value.includes(edge.id)),
  }))

  const hasSelection = computed(
    () => selectedNodes.value.length > 0 || selectedEdges.value.length > 0
  )

  const flowState = computed<FlowState>(() => ({
    nodes: nodes.value,
    edges: edges.value,
    selectedNodes: selectedNodes.value,
    selectedEdges: selectedEdges.value,
    viewport: viewport.value,
    isLoading: isLoading.value,
    isDirty: isDirty.value,
  }))

  // 节点操作
  const addNode = (node: Omit<WorkflowNode, 'id'>) => {
    const newNode: WorkflowNode = {
      ...node,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date(),
    }
    nodes.value.push(newNode)
    isDirty.value = true
    return newNode
  }

  const updateNode = (id: string, updates: Partial<WorkflowNode>) => {
    const index = nodes.value.findIndex(node => node.id === id)
    if (index !== -1) {
      nodes.value[index] = {
        ...nodes.value[index],
        ...updates,
        updatedAt: new Date(),
      }
      isDirty.value = true
    }
  }

  const removeNode = (id: string) => {
    const index = nodes.value.findIndex(node => node.id === id)
    if (index !== -1) {
      nodes.value.splice(index, 1)
      // 同时删除相关的边
      edges.value = edges.value.filter(edge => edge.source !== id && edge.target !== id)
      // 清除选择
      selectedNodes.value = selectedNodes.value.filter(nodeId => nodeId !== id)
      isDirty.value = true
    }
  }

  const removeNodes = (ids: string[]) => {
    ids.forEach(id => removeNode(id))
  }

  // 边操作
  const addEdge = (edge: Omit<WorkflowEdge, 'id'>) => {
    const newEdge: WorkflowEdge = {
      ...edge,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date(),
    }
    edges.value.push(newEdge)
    isDirty.value = true
    return newEdge
  }

  const updateEdge = (id: string, updates: Partial<WorkflowEdge>) => {
    const index = edges.value.findIndex(edge => edge.id === id)
    if (index !== -1) {
      edges.value[index] = {
        ...edges.value[index],
        ...updates,
        updatedAt: new Date(),
      }
      isDirty.value = true
    }
  }

  const removeEdge = (id: string) => {
    const index = edges.value.findIndex(edge => edge.id === id)
    if (index !== -1) {
      edges.value.splice(index, 1)
      selectedEdges.value = selectedEdges.value.filter(edgeId => edgeId !== id)
      isDirty.value = true
    }
  }

  const removeEdges = (ids: string[]) => {
    ids.forEach(id => removeEdge(id))
  }

  // 连接操作
  const addConnection = (connection: Connection) => {
    const edge = addEdge({
      source: connection.source,
      target: connection.target,
      sourceHandle: connection.sourceHandle,
      targetHandle: connection.targetHandle,
      type: (connection.type as EdgeType) || 'default',
    })
    return edge
  }

  // 选择操作
  const selectNode = (id: string, multiple = false) => {
    if (multiple) {
      if (!selectedNodes.value.includes(id)) {
        selectedNodes.value.push(id)
      }
    } else {
      selectedNodes.value = [id]
      selectedEdges.value = []
    }
  }

  const selectEdge = (id: string, multiple = false) => {
    if (multiple) {
      if (!selectedEdges.value.includes(id)) {
        selectedEdges.value.push(id)
      }
    } else {
      selectedEdges.value = [id]
      selectedNodes.value = []
    }
  }

  const selectAll = () => {
    selectedNodes.value = nodes.value.map(node => node.id)
    selectedEdges.value = edges.value.map(edge => edge.id)
  }

  const clearSelection = () => {
    selectedNodes.value = []
    selectedEdges.value = []
  }

  const deleteSelected = () => {
    removeNodes(selectedNodes.value)
    removeEdges(selectedEdges.value)
    clearSelection()
  }

  // 变更应用
  const applyNodeChanges = (changes: NodeChange[]) => {
    changes.forEach(change => {
      switch (change.type) {
        case 'position':
          if (change.position) {
            updateNode(change.id, { position: change.position })
          }
          break
        case 'dimensions':
          if (change.dimensions) {
            updateNode(change.id, {
              width: change.dimensions.width,
              height: change.dimensions.height,
            })
          }
          break
        case 'remove':
          removeNode(change.id)
          break
        case 'select':
          if (change.selected) {
            selectNode(change.id, true)
          } else {
            selectedNodes.value = selectedNodes.value.filter(id => id !== change.id)
          }
          break
      }
    })
  }

  const applyEdgeChanges = (changes: EdgeChange[]) => {
    changes.forEach(change => {
      switch (change.type) {
        case 'remove':
          removeEdge(change.id)
          break
        case 'select':
          if (change.selected) {
            selectEdge(change.id, true)
          } else {
            selectedEdges.value = selectedEdges.value.filter(id => id !== change.id)
          }
          break
      }
    })
  }

  // 视口操作
  const setViewport = (newViewport: Viewport) => {
    viewport.value = newViewport
  }

  const fitView = () => {
    // 实现适应视图逻辑
    // 这里需要与 VueFlow 实例配合
  }

  // 数据操作
  const loadFlow = (flowData: { nodes: WorkflowNode[]; edges: WorkflowEdge[] }) => {
    isLoading.value = true
    try {
      nodes.value = flowData.nodes
      edges.value = flowData.edges
      clearSelection()
      isDirty.value = false
    } finally {
      isLoading.value = false
    }
  }

  const clearFlow = () => {
    nodes.value = []
    edges.value = []
    clearSelection()
    isDirty.value = false
  }

  const exportFlow = () => {
    return {
      nodes: nodes.value,
      edges: edges.value,
      viewport: viewport.value,
    }
  }

  // 重置状态
  const $reset = () => {
    nodes.value = []
    edges.value = []
    selectedNodes.value = []
    selectedEdges.value = []
    viewport.value = { x: 0, y: 0, zoom: 1 }
    isLoading.value = false
    isDirty.value = false
  }

  return {
    // 状态
    nodes,
    edges,
    selectedNodes,
    selectedEdges,
    viewport,
    isLoading,
    isDirty,

    // 计算属性
    selectedElements,
    hasSelection,
    flowState,

    // 节点操作
    addNode,
    updateNode,
    removeNode,
    removeNodes,

    // 边操作
    addEdge,
    updateEdge,
    removeEdge,
    removeEdges,
    addConnection,

    // 选择操作
    selectNode,
    selectEdge,
    selectAll,
    clearSelection,
    deleteSelected,

    // 变更应用
    applyNodeChanges,
    applyEdgeChanges,

    // 视口操作
    setViewport,
    fitView,

    // 数据操作
    loadFlow,
    clearFlow,
    exportFlow,

    // 重置
    $reset,
  }
})
