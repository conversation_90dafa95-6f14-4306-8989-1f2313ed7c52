/**
 * 工作流状态管理组合式函数
 */
import { useFlowStore } from './flow'
import { useWorkflowStore } from './workflow'
import { useExecutionStore } from './execution'
import { useHistoryStore } from './history'

/**
 * 统一的工作流 stores 组合函数
 */
export function useFlowStores() {
  const flowStore = useFlowStore()
  const workflowStore = useWorkflowStore()
  const executionStore = useExecutionStore()
  const historyStore = useHistoryStore()

  return {
    flow: flowStore,
    workflow: workflowStore,
    execution: executionStore,
    history: historyStore,
  }
}

/**
 * 工作流编辑器组合函数
 */
export function useFlowEditor() {
  const { flow, workflow, history } = useFlowStores()

  // 创建新工作流
  const createNewWorkflow = (name: string) => {
    const newWorkflow = workflow.createWorkflow(name)
    workflow.setCurrentWorkflow(newWorkflow.id)
    flow.clearFlow()
    history.clear()
    return newWorkflow
  }

  // 加载工作流到编辑器
  const loadWorkflowToEditor = (workflowId: string) => {
    const workflowData = workflow.workflows.get(workflowId)
    if (!workflowData) {
      throw new Error(`Workflow ${workflowId} not found`)
    }

    workflow.setCurrentWorkflow(workflowId)
    flow.loadFlow({
      nodes: workflowData.nodes,
      edges: workflowData.edges,
    })
    history.clear()
  }

  // 保存当前编辑的工作流
  const saveCurrentWorkflow = async () => {
    if (!workflow.currentWorkflowId) {
      throw new Error('No workflow is currently selected')
    }

    const flowData = flow.exportFlow()
    workflow.updateWorkflow(workflow.currentWorkflowId, {
      nodes: flowData.nodes,
      edges: flowData.edges,
    })

    // 保存工作流（updateWorkflow 已经自动保存到 workflows Map 中）
    flow.isDirty = false
  }

  // 自动保存
  const enableAutoSave = (interval = 30000) => {
    return setInterval(async () => {
      if (flow.isDirty && workflow.currentWorkflowId) {
        try {
          await saveCurrentWorkflow()
        } catch (error) {
          console.warn('Auto-save failed:', error)
        }
      }
    }, interval)
  }

  return {
    createNewWorkflow,
    loadWorkflowToEditor,
    saveCurrentWorkflow,
    enableAutoSave,
  }
}

/**
 * 工作流执行组合函数
 */
export function useFlowExecution() {
  const { workflow, execution } = useFlowStores()

  // 执行当前工作流
  const executeCurrentWorkflow = async (context = {}) => {
    if (!workflow.currentWorkflowId) {
      throw new Error('No workflow is currently selected')
    }

    return execution.executeWorkflow(workflow.currentWorkflowId, context)
  }

  // 停止执行
  const stopExecution = (executionId: string) => {
    return execution.stopExecution(executionId)
  }

  // 获取执行历史
  const getExecutionHistory = (workflowId?: string) => {
    return execution.getExecutionHistory(workflowId)
  }

  return {
    executeCurrentWorkflow,
    stopExecution,
    getExecutionHistory,
  }
}
