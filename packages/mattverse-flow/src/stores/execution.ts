/**
 * 工作流执行状态管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  WorkflowExecutionResult,
  WorkflowExecutionContext,
  WorkflowExecutionLog,
} from '../types'
import { useWorkflowStore } from './workflow'
import { WorkflowEngine } from '../core/engine'

export const useExecutionStore = defineStore('mattverse-execution', () => {
  // 状态
  const executions = ref<Map<string, WorkflowExecutionResult>>(new Map())
  const currentExecutionId = ref<string | null>(null)
  const isExecuting = ref(false)

  // 工作流引擎实例
  const engine = new WorkflowEngine()

  // 计算属性
  const executionList = computed(() => Array.from(executions.value.values()))

  const currentExecution = computed(() =>
    currentExecutionId.value ? executions.value.get(currentExecutionId.value) : null
  )

  const runningExecutions = computed(() =>
    executionList.value.filter(execution => !execution.endTime)
  )

  const completedExecutions = computed(() =>
    executionList.value.filter(execution => execution.endTime && execution.success)
  )

  const failedExecutions = computed(() =>
    executionList.value.filter(execution => execution.endTime && !execution.success)
  )

  // 执行操作
  const executeWorkflow = async (
    workflowId: string,
    context: WorkflowExecutionContext = {}
  ): Promise<WorkflowExecutionResult> => {
    if (isExecuting.value) {
      throw new Error('Another workflow is currently executing')
    }

    isExecuting.value = true

    try {
      const result = await engine.executeWorkflow(workflowId, context)
      executions.value.set(result.executionId, result)
      currentExecutionId.value = result.executionId
      return result
    } finally {
      isExecuting.value = false
    }
  }

  const stopExecution = (executionId: string): boolean => {
    const success = engine.executor.stopExecution(executionId)

    if (success) {
      const execution = executions.value.get(executionId)
      if (execution && !execution.endTime) {
        const updatedExecution: WorkflowExecutionResult = {
          ...execution,
          success: false,
          endTime: new Date(),
          duration: new Date().getTime() - execution.startTime.getTime(),
          error: 'Execution was stopped by user',
          logs: [
            ...execution.logs,
            {
              id: `log-${Date.now()}`,
              level: 'warn',
              message: 'Execution stopped by user',
              timestamp: new Date(),
            },
          ],
        }
        executions.value.set(executionId, updatedExecution)
      }
    }

    return success
  }

  // 历史记录操作
  const getExecutionHistory = (workflowId?: string) => {
    if (workflowId) {
      return executionList.value.filter(execution => execution.workflowId === workflowId)
    }
    return executionList.value
  }

  const getExecutionById = (executionId: string) => {
    return executions.value.get(executionId)
  }

  const clearExecutionHistory = (workflowId?: string) => {
    if (workflowId) {
      for (const [id, execution] of executions.value) {
        if (execution.workflowId === workflowId) {
          executions.value.delete(id)
        }
      }
    } else {
      executions.value.clear()
    }

    if (currentExecutionId.value && !executions.value.has(currentExecutionId.value)) {
      currentExecutionId.value = null
    }
  }

  // 日志操作
  const getExecutionLogs = (executionId: string): WorkflowExecutionLog[] => {
    const execution = executions.value.get(executionId)
    return execution?.logs || []
  }

  const addExecutionLog = (executionId: string, log: Omit<WorkflowExecutionLog, 'id'>) => {
    const execution = executions.value.get(executionId)
    if (execution) {
      const newLog: WorkflowExecutionLog = {
        ...log,
        id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      }

      const updatedExecution = {
        ...execution,
        logs: [...execution.logs, newLog],
      }

      executions.value.set(executionId, updatedExecution)
    }
  }

  // 统计信息
  const getExecutionStats = (workflowId?: string) => {
    const relevantExecutions = workflowId
      ? executionList.value.filter(e => e.workflowId === workflowId)
      : executionList.value

    const total = relevantExecutions.length
    const completed = relevantExecutions.filter(e => e.endTime).length
    const successful = relevantExecutions.filter(e => e.success).length
    const failed = relevantExecutions.filter(e => e.endTime && !e.success).length
    const running = relevantExecutions.filter(e => !e.endTime).length

    const successRate = completed > 0 ? (successful / completed) * 100 : 0

    const avgDuration =
      relevantExecutions.filter(e => e.duration).reduce((sum, e) => sum + (e.duration || 0), 0) /
      Math.max(1, successful)

    return {
      total,
      completed,
      successful,
      failed,
      running,
      successRate: Math.round(successRate * 100) / 100,
      avgDuration: Math.round(avgDuration),
    }
  }

  // 重置状态
  const $reset = () => {
    executions.value.clear()
    currentExecutionId.value = null
    isExecuting.value = false
  }

  return {
    // 状态
    executions,
    currentExecutionId,
    isExecuting,

    // 计算属性
    executionList,
    currentExecution,
    runningExecutions,
    completedExecutions,
    failedExecutions,

    // 执行操作
    executeWorkflow,
    stopExecution,

    // 历史记录
    getExecutionHistory,
    getExecutionById,
    clearExecutionHistory,

    // 日志操作
    getExecutionLogs,
    addExecutionLog,

    // 统计
    getExecutionStats,

    // 重置
    $reset,
  }
})
