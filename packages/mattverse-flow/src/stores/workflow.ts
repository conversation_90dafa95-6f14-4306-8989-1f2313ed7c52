/**
 * 工作流管理状态
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { generateId } from '@mattverse/shared'
import type { WorkflowNode, WorkflowEdge, Workflow } from '../types'

export const useWorkflowStore = defineStore('mattverse-workflow', () => {
  // 状态
  const workflows = ref<Map<string, Workflow>>(new Map())
  const currentWorkflowId = ref<string | null>(null)

  // 计算属性
  const workflowList = computed(() => Array.from(workflows.value.values()))

  const currentWorkflow = computed(() =>
    currentWorkflowId.value ? workflows.value.get(currentWorkflowId.value) : null
  )

  const publishedWorkflows = computed(() =>
    workflowList.value.filter(w => w.status === 'published')
  )

  const draftWorkflows = computed(() => workflowList.value.filter(w => w.status === 'draft'))

  // 工作流操作
  const createWorkflow = (name: string, options: Partial<Workflow> = {}): Workflow => {
    const workflow: Workflow = {
      id: generateId(),
      name,
      description: options.description || '',
      nodes: options.nodes || [],
      edges: options.edges || [],
      createdAt: new Date(),
      updatedAt: new Date(),
      publishedAt: options.publishedAt,
      version: options.version || '1.0.0',
      status: options.status || 'draft',
      tags: options.tags || [],
      metadata: options.metadata || {},
    }

    workflows.value.set(workflow.id, workflow)
    return workflow
  }

  const updateWorkflow = (id: string, updates: Partial<Workflow>) => {
    const workflow = workflows.value.get(id)
    if (workflow) {
      const updatedWorkflow = {
        ...workflow,
        ...updates,
        updatedAt: new Date(),
      }
      workflows.value.set(id, updatedWorkflow)
    }
  }

  const deleteWorkflow = (id: string) => {
    workflows.value.delete(id)
    if (currentWorkflowId.value === id) {
      currentWorkflowId.value = null
    }
  }

  const duplicateWorkflow = (id: string, newName?: string): Workflow | null => {
    const workflow = workflows.value.get(id)
    if (workflow) {
      const duplicated = createWorkflow(newName || `${workflow.name} (副本)`, {
        ...workflow,
        nodes: JSON.parse(JSON.stringify(workflow.nodes)),
        edges: JSON.parse(JSON.stringify(workflow.edges)),
      })
      return duplicated
    }
    return null
  }

  // 工作流查询
  const getWorkflow = (id: string) => {
    return workflows.value.get(id)
  }

  const getWorkflowsByTag = (tag: string) => {
    return workflowList.value.filter(w => w.tags.includes(tag))
  }

  const searchWorkflows = (query: string) => {
    const lowerQuery = query.toLowerCase()
    return workflowList.value.filter(
      w =>
        w.name.toLowerCase().includes(lowerQuery) ||
        w.description?.toLowerCase().includes(lowerQuery) ||
        w.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    )
  }

  // 当前工作流操作
  const setCurrentWorkflow = (id: string | null) => {
    currentWorkflowId.value = id
  }

  const publishWorkflow = (id: string) => {
    const workflow = workflows.value.get(id)
    if (workflow) {
      updateWorkflow(id, {
        status: 'published',
        publishedAt: new Date(),
      })
    }
  }

  const archiveWorkflow = (id: string) => {
    updateWorkflow(id, { status: 'archived' })
  }

  const restoreWorkflow = (id: string) => {
    updateWorkflow(id, { status: 'draft' })
  }

  // 版本管理
  const createVersion = (id: string, newVersion: string) => {
    const workflow = workflows.value.get(id)
    if (workflow) {
      const versionedWorkflow = duplicateWorkflow(id, `${workflow.name} v${newVersion}`)
      if (versionedWorkflow) {
        updateWorkflow(versionedWorkflow.id, { version: newVersion })
        return versionedWorkflow
      }
    }
    return null
  }

  // 导入导出
  const exportWorkflow = (id: string) => {
    const workflow = workflows.value.get(id)
    if (workflow) {
      return {
        ...workflow,
        exportedAt: new Date().toISOString(),
      }
    }
    return null
  }

  const importWorkflow = (workflowData: any): Workflow => {
    const workflow = createWorkflow(workflowData.name, {
      ...workflowData,
      id: undefined, // 生成新的 ID
    })
    return workflow
  }

  // 统计信息
  const getWorkflowStats = () => {
    const total = workflows.value.size
    const published = publishedWorkflows.value.length
    const drafts = draftWorkflows.value.length
    const archived = workflowList.value.filter(w => w.status === 'archived').length

    return {
      total,
      published,
      drafts,
      archived,
    }
  }

  // 重置状态
  const $reset = () => {
    workflows.value.clear()
    currentWorkflowId.value = null
  }

  return {
    // 状态
    workflows,
    currentWorkflowId,

    // 计算属性
    workflowList,
    currentWorkflow,
    publishedWorkflows,
    draftWorkflows,

    // 基础操作
    createWorkflow,
    updateWorkflow,
    deleteWorkflow,
    duplicateWorkflow,

    // 查询操作
    getWorkflow,
    getWorkflowsByTag,
    searchWorkflows,

    // 当前工作流
    setCurrentWorkflow,

    // 状态管理
    publishWorkflow,
    archiveWorkflow,
    restoreWorkflow,

    // 版本管理
    createVersion,

    // 导入导出
    exportWorkflow,
    importWorkflow,

    // 统计
    getWorkflowStats,

    // 重置
    $reset,
  }
})
