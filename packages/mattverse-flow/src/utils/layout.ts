/**
 * 布局工具函数
 */
import type { WorkflowNode, Position } from '../types'

/**
 * 自动布局算法 - 简单的层次布局
 */
export function autoLayout(nodes: WorkflowNode[]): WorkflowNode[] {
  if (nodes.length === 0) return nodes

  // 简单的网格布局
  const gridSize = 200
  const cols = Math.ceil(Math.sqrt(nodes.length))

  return nodes.map((node, index) => ({
    ...node,
    position: {
      x: (index % cols) * gridSize + 100,
      y: Math.floor(index / cols) * gridSize + 100,
    },
  }))
}

/**
 * 层次布局 - 根据节点类型分层
 */
export function hierarchicalLayout(nodes: WorkflowNode[]): WorkflowNode[] {
  const layers: { [key: string]: WorkflowNode[] } = {
    start: [],
    process: [],
    decision: [],
    end: [],
  }

  // 按类型分组
  nodes.forEach(node => {
    if (layers[node.type]) {
      layers[node.type].push(node)
    } else {
      layers.process.push(node)
    }
  })

  const result: WorkflowNode[] = []
  let currentY = 100
  const layerHeight = 150
  const nodeSpacing = 200

  // 为每一层布局节点
  Object.entries(layers).forEach(([type, layerNodes]) => {
    if (layerNodes.length === 0) return

    const startX = 100
    layerNodes.forEach((node, index) => {
      result.push({
        ...node,
        position: {
          x: startX + index * nodeSpacing,
          y: currentY,
        },
      })
    })

    currentY += layerHeight
  })

  return result
}

/**
 * 圆形布局
 */
export function circularLayout(
  nodes: WorkflowNode[],
  center: Position = { x: 300, y: 300 },
  radius: number = 200
): WorkflowNode[] {
  if (nodes.length === 0) return nodes

  const angleStep = (2 * Math.PI) / nodes.length

  return nodes.map((node, index) => ({
    ...node,
    position: {
      x: center.x + radius * Math.cos(index * angleStep),
      y: center.y + radius * Math.sin(index * angleStep),
    },
  }))
}

/**
 * 力导向布局 - 简化版本
 */
export function forceLayout(nodes: WorkflowNode[], iterations: number = 50): WorkflowNode[] {
  if (nodes.length === 0) return nodes

  const result = nodes.map(node => ({ ...node }))
  const repulsionForce = 1000
  const attractionForce = 0.1
  const damping = 0.9

  for (let iter = 0; iter < iterations; iter++) {
    const forces: Position[] = result.map(() => ({ x: 0, y: 0 }))

    // 计算排斥力
    for (let i = 0; i < result.length; i++) {
      for (let j = i + 1; j < result.length; j++) {
        const dx = result[j].position.x - result[i].position.x
        const dy = result[j].position.y - result[i].position.y
        const distance = Math.sqrt(dx * dx + dy * dy) || 1

        const force = repulsionForce / (distance * distance)
        const fx = (dx / distance) * force
        const fy = (dy / distance) * force

        forces[i].x -= fx
        forces[i].y -= fy
        forces[j].x += fx
        forces[j].y += fy
      }
    }

    // 应用力并更新位置
    result.forEach((node, index) => {
      node.position.x += forces[index].x * damping
      node.position.y += forces[index].y * damping
    })
  }

  return result
}

/**
 * 对齐节点到网格
 */
export function alignToGrid(nodes: WorkflowNode[], gridSize: number = 20): WorkflowNode[] {
  return nodes.map(node => ({
    ...node,
    position: {
      x: Math.round(node.position.x / gridSize) * gridSize,
      y: Math.round(node.position.y / gridSize) * gridSize,
    },
  }))
}

/**
 * 计算节点的边界框
 */
export function getNodesBounds(nodes: WorkflowNode[]) {
  if (nodes.length === 0) {
    return { x: 0, y: 0, width: 0, height: 0 }
  }

  let minX = nodes[0].position.x
  let minY = nodes[0].position.y
  let maxX = nodes[0].position.x + (nodes[0].width || 150)
  let maxY = nodes[0].position.y + (nodes[0].height || 80)

  nodes.forEach(node => {
    const nodeWidth = node.width || 150
    const nodeHeight = node.height || 80

    minX = Math.min(minX, node.position.x)
    minY = Math.min(minY, node.position.y)
    maxX = Math.max(maxX, node.position.x + nodeWidth)
    maxY = Math.max(maxY, node.position.y + nodeHeight)
  })

  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY,
  }
}
