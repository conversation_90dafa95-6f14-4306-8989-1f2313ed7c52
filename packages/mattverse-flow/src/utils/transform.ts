/**
 * 数据转换工具函数
 */
import type { WorkflowNode, WorkflowEdge, Position } from '../types'

/**
 * 转换屏幕坐标到画布坐标
 */
export function screenToCanvas(
  screenPosition: Position,
  viewport: { x: number; y: number; zoom: number }
): Position {
  return {
    x: (screenPosition.x - viewport.x) / viewport.zoom,
    y: (screenPosition.y - viewport.y) / viewport.zoom,
  }
}

/**
 * 转换画布坐标到屏幕坐标
 */
export function canvasToScreen(
  canvasPosition: Position,
  viewport: { x: number; y: number; zoom: number }
): Position {
  return {
    x: canvasPosition.x * viewport.zoom + viewport.x,
    y: canvasPosition.y * viewport.zoom + viewport.y,
  }
}

/**
 * 深拷贝节点
 */
export function cloneNode(node: WorkflowNode): WorkflowNode {
  return {
    ...node,
    position: { ...node.position },
    data: { ...node.data },
  }
}

/**
 * 深拷贝边
 */
export function cloneEdge(edge: WorkflowEdge): WorkflowEdge {
  return {
    ...edge,
    data: { ...edge.data },
  }
}

/**
 * 批量更新节点位置
 */
export function translateNodes(nodes: WorkflowNode[], delta: Position): WorkflowNode[] {
  return nodes.map(node => ({
    ...node,
    position: {
      x: node.position.x + delta.x,
      y: node.position.y + delta.y,
    },
  }))
}

/**
 * 缩放节点位置
 */
export function scaleNodes(
  nodes: WorkflowNode[],
  scale: number,
  origin: Position = { x: 0, y: 0 }
): WorkflowNode[] {
  return nodes.map(node => ({
    ...node,
    position: {
      x: origin.x + (node.position.x - origin.x) * scale,
      y: origin.y + (node.position.y - origin.y) * scale,
    },
  }))
}

/**
 * 对齐节点到网格
 */
export function snapToGrid(position: Position, gridSize: number): Position {
  return {
    x: Math.round(position.x / gridSize) * gridSize,
    y: Math.round(position.y / gridSize) * gridSize,
  }
}

/**
 * 计算两点之间的距离
 */
export function distance(p1: Position, p2: Position): number {
  const dx = p2.x - p1.x
  const dy = p2.y - p1.y
  return Math.sqrt(dx * dx + dy * dy)
}

/**
 * 计算边界框
 */
export function getBoundingBox(positions: Position[]) {
  if (positions.length === 0) {
    return { x: 0, y: 0, width: 0, height: 0 }
  }

  let minX = positions[0].x
  let minY = positions[0].y
  let maxX = positions[0].x
  let maxY = positions[0].y

  for (const pos of positions) {
    minX = Math.min(minX, pos.x)
    minY = Math.min(minY, pos.y)
    maxX = Math.max(maxX, pos.x)
    maxY = Math.max(maxY, pos.y)
  }

  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY,
  }
}
