/**
 * @mattverse/mattverse-flow
 * 工作流编辑器包主入口
 */

// 状态管理
export * from './stores/flow'
export * from './stores/workflow'
export * from './stores/execution'
export * from './stores/history'

// 核心类
export * from './core/engine'
export * from './core/executor'
export * from './core/validator'
export * from './core/serializer'

// 类型定义
export * from './types'

// 工具函数
export * from './utils'

// 常量
export * from './constants'

// Composables
export * from './composables/useFlowStores'
export * from './composables/useFlowEditor'
export * from './composables/useFlowExecution'

// Vue 组件
export { default as FlowCanvas } from './components/FlowCanvas.vue'
export { default as NodePalette } from './components/NodePalette.vue'
export { default as PropertyPanel } from './components/PropertyPanel.vue'
