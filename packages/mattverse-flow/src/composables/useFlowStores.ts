/**
 * 工作流状态管理 Composable
 */
import { useFlowStore } from '../stores/flow'
import { useWorkflowStore } from '../stores/workflow'
import { useExecutionStore } from '../stores/execution'
import { useHistoryStore } from '../stores/history'

export function useFlowStores() {
  const flow = useFlowStore()
  const workflow = useWorkflowStore()
  const execution = useExecutionStore()
  const history = useHistoryStore()

  return {
    flow,
    workflow,
    execution,
    history,
  }
}
