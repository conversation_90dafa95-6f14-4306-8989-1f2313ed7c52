/**
 * 工作流编辑器 Composable
 */
import { computed } from 'vue'
import { useFlowStores } from './useFlowStores'
import type { WorkflowNode, WorkflowEdge } from '../types'

export function useFlowEditor() {
  const { flow, workflow, history } = useFlowStores()

  // 创建新工作流
  const createNewWorkflow = (name: string, options?: any) => {
    const newWorkflow = workflow.createWorkflow(name, options)
    flow.clearFlow()
    history.clear()
    return newWorkflow
  }

  // 加载工作流到编辑器
  const loadWorkflowToEditor = (workflowId: string) => {
    const workflowData = workflow.getWorkflow(workflowId)
    if (workflowData) {
      flow.loadFlow({
        nodes: workflowData.nodes,
        edges: workflowData.edges,
      })
      workflow.setCurrentWorkflow(workflowId)
      history.clear()
    }
  }

  // 保存当前工作流
  const saveCurrentWorkflow = async () => {
    const currentWorkflow = workflow.currentWorkflow
    if (currentWorkflow) {
      const updatedWorkflow = {
        ...currentWorkflow,
        nodes: flow.nodes,
        edges: flow.edges,
        updatedAt: new Date(),
      }
      workflow.updateWorkflow(currentWorkflow.id, updatedWorkflow)
      flow.isDirty = false
    }
  }

  // 添加节点（带历史记录）
  const addNodeWithHistory = (nodeData: Omit<WorkflowNode, 'id'>) => {
    const node = flow.addNode(nodeData)
    history.recordNodeAdd(node)
    return node
  }

  // 删除节点（带历史记录）
  const removeNodeWithHistory = (nodeId: string) => {
    const node = flow.nodes.find(n => n.id === nodeId)
    if (node) {
      history.recordNodeRemove(node)
      flow.removeNode(nodeId)
    }
  }

  // 添加边（带历史记录）
  const addEdgeWithHistory = (edgeData: Omit<WorkflowEdge, 'id'>) => {
    const edge = flow.addEdge(edgeData)
    history.recordEdgeAdd(edge)
    return edge
  }

  // 删除边（带历史记录）
  const removeEdgeWithHistory = (edgeId: string) => {
    const edge = flow.edges.find(e => e.id === edgeId)
    if (edge) {
      history.recordEdgeRemove(edge)
      flow.removeEdge(edgeId)
    }
  }

  // 撤销操作
  const undo = () => {
    const action = history.undo()
    if (action) {
      applyHistoryAction(action, 'undo')
    }
  }

  // 重做操作
  const redo = () => {
    const action = history.redo()
    if (action) {
      applyHistoryAction(action, 'redo')
    }
  }

  // 应用历史操作
  const applyHistoryAction = (action: any, direction: 'undo' | 'redo') => {
    const isUndo = direction === 'undo'

    switch (action.type) {
      case 'add-node':
        if (isUndo) {
          flow.removeNode(action.data.node.id)
        } else {
          flow.nodes.push(action.data.node)
        }
        break
      case 'remove-node':
        if (isUndo) {
          flow.nodes.push(action.data.node)
        } else {
          flow.removeNode(action.data.node.id)
        }
        break
      case 'add-edge':
        if (isUndo) {
          flow.removeEdge(action.data.edge.id)
        } else {
          flow.edges.push(action.data.edge)
        }
        break
      case 'remove-edge':
        if (isUndo) {
          flow.edges.push(action.data.edge)
        } else {
          flow.removeEdge(action.data.edge.id)
        }
        break
    }
  }

  // 计算属性
  const canUndo = computed(() => history.canUndo)
  const canRedo = computed(() => history.canRedo)
  const isDirty = computed(() => flow.isDirty)

  return {
    // 工作流操作
    createNewWorkflow,
    loadWorkflowToEditor,
    saveCurrentWorkflow,

    // 节点操作
    addNodeWithHistory,
    removeNodeWithHistory,

    // 边操作
    addEdgeWithHistory,
    removeEdgeWithHistory,

    // 历史操作
    undo,
    redo,

    // 状态
    canUndo,
    canRedo,
    isDirty,
  }
}
