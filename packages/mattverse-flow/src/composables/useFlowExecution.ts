/**
 * 工作流执行 Composable
 */
import { computed } from 'vue'
import { useFlowStores } from './useFlowStores'
import type { WorkflowExecutionContext } from '../types'

export function useFlowExecution() {
  const { workflow, execution, flow } = useFlowStores()

  // 执行当前工作流
  const executeCurrentWorkflow = async (context: WorkflowExecutionContext = {}) => {
    const currentWorkflow = workflow.currentWorkflow
    if (!currentWorkflow) {
      throw new Error('No workflow selected')
    }

    if (flow.nodes.length === 0) {
      throw new Error('Workflow has no nodes')
    }

    return await execution.executeWorkflow(currentWorkflow.id, context)
  }

  // 停止执行
  const stopCurrentExecution = () => {
    const currentExecution = execution.currentExecution
    if (currentExecution) {
      return execution.stopExecution(currentExecution.executionId)
    }
    return false
  }

  // 获取执行历史
  const getExecutionHistory = () => {
    const currentWorkflow = workflow.currentWorkflow
    if (currentWorkflow) {
      return execution.getExecutionHistory(currentWorkflow.id)
    }
    return []
  }

  // 获取执行统计
  const getExecutionStats = () => {
    const currentWorkflow = workflow.currentWorkflow
    if (currentWorkflow) {
      return execution.getExecutionStats(currentWorkflow.id)
    }
    return {
      total: 0,
      completed: 0,
      successful: 0,
      failed: 0,
      running: 0,
      successRate: 0,
      avgDuration: 0,
    }
  }

  // 计算属性
  const isExecuting = computed(() => execution.isExecuting)
  const currentExecution = computed(() => execution.currentExecution)
  const canExecute = computed(() => {
    return workflow.currentWorkflow && flow.nodes.length > 0 && !execution.isExecuting
  })

  return {
    // 执行操作
    executeCurrentWorkflow,
    stopCurrentExecution,

    // 历史和统计
    getExecutionHistory,
    getExecutionStats,

    // 状态
    isExecuting,
    currentExecution,
    canExecute,
  }
}
