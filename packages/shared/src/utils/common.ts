/**
 * 将字符串从 snake_case 转换为 camelCase
 * @param str snake_case 格式的字符串
 * @returns camelCase 格式的字符串
 */
export function toCamelCase(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 递归地将对象的所有属性从 snake_case 转换为 camelCase
 * @param obj 要转换的对象
 * @returns 转换后的对象
 */
export function convertToCamelCase(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(item => convertToCamelCase(item))
  }
  if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((result, key) => {
      const camelKey = toCamelCase(key)
      result[camelKey] = convertToCamelCase(obj[key])
      return result
    }, {} as any)
  }
  return obj
}

/**
 * 生成唯一 ID
 * @param prefix 前缀
 * @returns 唯一 ID
 */
export function generateId(prefix: string = 'id'): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substring(2, 8)
  return `${prefix}_${timestamp}_${random}`
}

/**
 * 生成短 ID (8位)
 * @returns 短 ID
 */
export function generateShortId(): string {
  return Math.random().toString(36).substring(2, 10)
}

/**
 * 生成 UUID v4
 * @returns UUID
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}
