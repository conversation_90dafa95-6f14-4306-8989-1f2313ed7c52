<template>
  <div class="title-bar">
    <!-- 左侧区域 -->
    <div class="left-section">
      <slot name="left">
        <img v-if="showLogo" :src="logoSrc" alt="Logo" class="logo" />
      </slot>
    </div>

    <!-- 中间标题区域 -->
    <div class="title-section">
      <slot name="title">{{ title }}</slot>
    </div>

    <!-- 右侧区域 -->
    <div class="right-section">
      <!-- 图标 -->
      <img v-if="showRightIcon" :src="iconSrc" alt="Icon" class="right-icon" />

      <!-- 窗口控制按钮 -->
      <div class="window-controls">
        <button class="window-control minimize" @click="minimize" :disabled="!isElectron">
          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
            <path d="M2 6h8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
          </svg>
        </button>
        <button class="window-control maximize" @click="maximize" :disabled="!isElectron">
          <svg v-if="isMaximized" width="12" height="12" viewBox="0 0 12 12" fill="none">
            <path d="M3 3h6v6H3V3z" stroke="currentColor" stroke-width="1.5" fill="none" />
            <path d="M5 5V2h5v5h-3" stroke="currentColor" stroke-width="1.5" fill="none" />
          </svg>
          <svg v-else width="12" height="12" viewBox="0 0 12 12" fill="none">
            <rect
              x="2"
              y="2"
              width="8"
              height="8"
              stroke="currentColor"
              stroke-width="1.5"
              fill="none"
            />
          </svg>
        </button>
        <button class="window-control close" @click="close" :disabled="!isElectron">
          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
            <path
              d="M3 3l6 6M9 3l-6 6"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'

// 声明全局类型
declare global {
  interface Window {
    electronAPI?: {
      invoke: (channel: string, ...args: any[]) => Promise<any>
      on: (channel: string, callback: (...args: any[]) => void) => void
      removeListener: (channel: string, callback: (...args: any[]) => void) => void
    }
  }
}

interface Props {
  title?: string
  showLogo?: boolean
  logoSrc?: string
  showRightIcon?: boolean
  rightIconSrc?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: 'MattVerse',
  showLogo: false,
  logoSrc: '',
  showRightIcon: false,
  rightIconSrc: '',
})

const isMaximized = ref(false)

// 检查是否在Electron环境中
const isElectron = computed(() => {
  return typeof window !== 'undefined' && window.electronAPI
})

// 获取图标路径
const iconSrc = computed(() => {
  return props.rightIconSrc || ''
})

// 窗口控制函数
const minimize = async () => {
  if (!isElectron.value) return

  try {
    await window.electronAPI?.invoke('window:minimize')
  } catch (error) {
    console.error('Failed to minimize window:', error)
  }
}

const maximize = async () => {
  if (!isElectron.value) return

  try {
    await window.electronAPI?.invoke('window:maximize')
  } catch (error) {
    console.error('Failed to maximize window:', error)
  }
}

const close = async () => {
  if (!isElectron.value) return

  try {
    await window.electronAPI?.invoke('window:close')
  } catch (error) {
    console.error('Failed to close window:', error)
  }
}

// 获取窗口状态
const updateWindowState = async () => {
  if (!isElectron.value) return

  try {
    const state = await window.electronAPI?.invoke('window:get-state')
    if (state) {
      isMaximized.value = state.isMaximized
    }
  } catch (error) {
    console.error('Failed to get window state:', error)
  }
}

// 监听窗口状态变化
const handleWindowStateChange = () => {
  updateWindowState()
}

onMounted(async () => {
  if (!isElectron.value) {
    console.warn('TitleBar component is not running in Electron environment')
    return
  }

  // 获取初始窗口状态
  await updateWindowState()

  // 监听窗口状态变化事件
  // 注意：这里需要根据实际的事件名称调整
  if (window.electronAPI?.on) {
    window.electronAPI.on('window-state-changed', handleWindowStateChange)
  }
})

onUnmounted(() => {
  if (!isElectron.value) return

  // 移除事件监听
  if (window.electronAPI?.removeListener) {
    window.electronAPI.removeListener('window-state-changed', handleWindowStateChange)
  }
})
</script>

<style lang="scss" scoped>
.title-bar {
  display: flex;
  align-items: center;
  height: 32px;
  background-color: #3c3c3c;
  color: white;
  user-select: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
  -webkit-app-region: drag;
}

.left-section {
  display: flex;
  align-items: center;
  padding-left: 10px;
  min-width: 120px;
}

.logo {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.title-section {
  flex: 1;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  pointer-events: none;
}

.right-section {
  display: flex;
  align-items: center;
  padding-right: 10px;
  min-width: 120px;
  justify-content: flex-end;
}

.right-icon {
  width: 16px;
  height: 16px;
  margin-left: 8px;
  object-fit: contain;
}

.window-controls {
  display: flex;
  -webkit-app-region: no-drag;
}

.window-control {
  width: 46px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &.close:hover:not(:disabled) {
    background-color: #e81123;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  svg {
    width: 12px;
    height: 12px;
  }
}
</style>
