<template>
  <div class="title-bar">
    <div class="drag-region">
      <!-- 左侧区域 -->
      <div class="left-section">
        <slot name="left">
          <!-- 左侧logo -->
          <img v-if="showLogo" :src="logoSrc" alt="MattVerse Logo" class="logo" />
        </slot>
      </div>

      <!-- 中间标题 -->
      <div class="title">
        <slot name="title">{{ title }}</slot>
      </div>

      <!-- 右侧图标区域 -->
      <div class="right-section">
        <slot name="right">
          <img v-if="showRightIcon" :src="iconSrc" alt="MattVerse Icon" class="right-icon" />
        </slot>
      </div>

      <!-- 右侧窗口控制按钮 -->
      <div class="window-controls">
        <button class="window-control minimize" @click="minimize" :disabled="!isElectron">
          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
            <path d="M2 6h8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
          </svg>
        </button>
        <button class="window-control maximize" @click="maximize" :disabled="!isElectron">
          <svg v-if="isMaximized" width="12" height="12" viewBox="0 0 12 12" fill="none">
            <path d="M3 3h6v6H3V3z" stroke="currentColor" stroke-width="1.5" fill="none" />
            <path d="M5 5V2h5v5h-3" stroke="currentColor" stroke-width="1.5" fill="none" />
          </svg>
          <svg v-else width="12" height="12" viewBox="0 0 12 12" fill="none">
            <rect
              x="2"
              y="2"
              width="8"
              height="8"
              stroke="currentColor"
              stroke-width="1.5"
              fill="none"
            />
          </svg>
        </button>
        <button class="window-control close" @click="close" :disabled="!isElectron">
          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
            <path
              d="M3 3l6 6M9 3l-6 6"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'

// 声明全局类型
declare global {
  interface Window {
    electronAPI?: {
      invoke: (channel: string, ...args: any[]) => Promise<any>
      on: (channel: string, callback: (...args: any[]) => void) => void
      removeListener: (channel: string, callback: (...args: any[]) => void) => void
    }
  }
}

interface Props {
  title?: string
  showLogo?: boolean
  logoSrc?: string
  showRightIcon?: boolean
  rightIconSrc?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: 'MattVerse',
  showLogo: false,
  logoSrc: '',
  showRightIcon: false,
  rightIconSrc: '',
})

const isMaximized = ref(false)

// 检查是否在Electron环境中
const isElectron = computed(() => {
  return typeof window !== 'undefined' && window.electronAPI
})

// 获取图标路径
const iconSrc = computed(() => {
  return props.rightIconSrc || ''
})

// 窗口控制函数
const minimize = async () => {
  if (!isElectron.value) return

  try {
    await window.electronAPI?.invoke('window:minimize')
  } catch (error) {
    console.error('Failed to minimize window:', error)
  }
}

const maximize = async () => {
  if (!isElectron.value) return

  try {
    await window.electronAPI?.invoke('window:maximize')
  } catch (error) {
    console.error('Failed to maximize window:', error)
  }
}

const close = async () => {
  if (!isElectron.value) return

  try {
    await window.electronAPI?.invoke('window:close')
  } catch (error) {
    console.error('Failed to close window:', error)
  }
}

// 获取窗口状态
const updateWindowState = async () => {
  if (!isElectron.value) return

  try {
    const state = await window.electronAPI?.invoke('window:get-state')
    if (state) {
      isMaximized.value = state.isMaximized
    }
  } catch (error) {
    console.error('Failed to get window state:', error)
  }
}

// 监听窗口状态变化
const handleWindowStateChange = () => {
  updateWindowState()
}

onMounted(async () => {
  if (!isElectron.value) {
    console.warn('TitleBar component is not running in Electron environment')
    return
  }

  // 获取初始窗口状态
  await updateWindowState()

  // 监听窗口状态变化事件
  // 注意：这里需要根据实际的事件名称调整
  if (window.electronAPI?.on) {
    window.electronAPI.on('window-state-changed', handleWindowStateChange)
  }
})

onUnmounted(() => {
  if (!isElectron.value) return

  // 移除事件监听
  if (window.electronAPI?.removeListener) {
    window.electronAPI.removeListener('window-state-changed', handleWindowStateChange)
  }
})
</script>

<style lang="scss" scoped>
.title-bar {
  @apply h-8 bg-[#3C3C3C] text-white select-none flex items-center;
  -webkit-app-region: drag;
  position: relative;
  z-index: 1000;
}

.drag-region {
  @apply flex items-center w-full h-full;
}

.left-section {
  @apply pl-2.5 flex items-center;
  min-width: 120px;
}

.logo {
  @apply w-5 h-5 mr-2;
}

.title {
  @apply flex-1 text-center text-sm font-medium;
  pointer-events: none;
}

.right-section {
  @apply pr-2.5 flex items-center;
  min-width: 120px;
  justify-content: flex-end;
}

.right-icon {
  @apply w-5 h-5 ml-2;
}

.window-controls {
  @apply flex;
  -webkit-app-region: no-drag;
}

.window-control {
  @apply w-[46px] h-8 flex justify-center items-center bg-transparent border-none text-white cursor-pointer transition-colors duration-200;

  &:hover:not(:disabled) {
    @apply bg-white bg-opacity-10;
  }

  &.close:hover:not(:disabled) {
    @apply bg-[#e81123];
  }

  &:disabled {
    @apply opacity-50 cursor-not-allowed;
  }

  svg {
    @apply w-3 h-3;
  }
}

/* 在非Electron环境下隐藏拖拽区域样式 */
.title-bar:not(.electron-env) {
  -webkit-app-region: initial;
}
</style>
