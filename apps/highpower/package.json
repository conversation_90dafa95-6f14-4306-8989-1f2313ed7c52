{"name": "highpower", "version": "1.1.0", "private": true, "description": "HighPower - High-performance computing workflow platform", "author": "mattverse.com", "homepage": "https://mattverse.com", "main": "out/main/index.js", "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview", "build:win": "pnpm run build && electron-builder --win", "build:mac": "pnpm run build && electron-builder --mac", "build:linux": "pnpm run build && electron-builder --linux", "clean": "rimraf dist release", "lint": "eslint src --ext .ts,.tsx,.vue", "lint:fix": "eslint src --ext .ts,.tsx,.vue --fix", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@mattverse/shared": "workspace:*", "@mattverse/mattverse-ui": "workspace:*", "@mattverse/mattverse-flow": "workspace:*", "@mattverse/electron-core": "workspace:*", "@mattverse/i18n": "workspace:*", "@msgpack/msgpack": "^3.0.0", "vue": "^3.5.0", "pinia": "^2.3.0", "vue-router": "^4.5.0"}, "devDependencies": {"@mattverse/configs": "workspace:*"}}