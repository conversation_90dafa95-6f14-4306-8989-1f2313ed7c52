<template>
  <div class="space-y-1">
    <div v-for="m in entries" :key="m[0]" class="mb-1">
      <div
        class="group flex items-center gap-2 px-2 py-2 rounded-md hover:bg-muted cursor-pointer transition"
        :class="{ 'bg-muted ring-1 ring-border': isSelected('module', m[0]) }"
      >
        <button class="p-1 rounded hover:bg-accent" @click.stop="toggleModuleExpand(m[0])">
          <MattIcon
            :name="isModuleExpanded(m[0]) ? 'ChevronDown' : 'ChevronRight'"
            class="w-4 h-4"
          />
        </button>
        <button class="flex-1 text-left truncate" @click="$emit('select:module', m[0])">
          <div class="flex items-center gap-2">
            <MattIcon name="Package" class="w-4 h-4" />
            <span class="truncate">{{ m[1].name }}</span>
            <Badge v-if="!m[1].enabled" variant="secondary">禁用</Badge>
          </div>
        </button>
      </div>

      <div v-show="isModuleExpanded(m[0])" class="ml-6 mt-1">
        <div v-for="c in m[1].categories || []" :key="m[0] + '::' + c.name" class="mb-1">
          <div
            class="flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-pointer"
            :class="{ 'bg-muted ring-1 ring-border': isSelected('category', m[0], c.name) }"
          >
            <button
              class="p-1 rounded hover:bg-accent"
              @click.stop="toggleCategoryExpand(m[0], c.name)"
            >
              <MattIcon
                :name="isCategoryExpanded(m[0], c.name) ? 'ChevronDown' : 'ChevronRight'"
                class="w-4 h-4"
              />
            </button>
            <button
              class="flex-1 text-left truncate"
              @click="$emit('select:category', m[0], c.name)"
            >
              <div class="flex items-center gap-2">
                <MattIcon name="Folder" class="w-4 h-4" />
                <span class="truncate">{{ c.name }}</span>
              </div>
            </button>
          </div>

          <div v-show="isCategoryExpanded(m[0], c.name)" class="ml-6 mt-1">
            <div v-for="n in c.nodes" :key="n.id">
              <button
                class="w-full flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted text-left"
                :class="{ 'bg-muted ring-1 ring-border': isSelected('node', m[0], c.name, n.id) }"
                @click="$emit('select:node', m[0], c.name, n.id)"
              >
                <span class="w-1.5 h-1.5 rounded-full bg-muted-foreground/60"></span>
                <span class="truncate">{{ n.data?.label || n.type }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <Separator class="my-2" />
    </div>
  </div>
</template>

<script setup lang="ts">
type Selection =
  | { type: null }
  | { type: 'module'; moduleName: string }
  | { type: 'category'; moduleName: string; categoryName: string }
  | { type: 'node'; moduleName: string; categoryName: string; nodeId: string }

const props = defineProps<{
  entries: Array<[string, any]>
  selected: Selection
  expandedModules?: Record<string, boolean>
  expandedCategories?: Record<string, Record<string, boolean>>
}>()

const emit = defineEmits<{
  'select:module': [moduleName: string]
  'select:category': [moduleName: string, categoryName: string]
  'select:node': [moduleName: string, categoryName: string, nodeId: string]
  'toggle:module': [moduleName: string]
  'toggle:category': [moduleName: string, categoryName: string]
}>()

function isSelected(
  t: Selection['type'],
  moduleName?: string,
  categoryName?: string,
  nodeId?: string
) {
  const s = props.selected
  if (!s || s.type !== t) return false

  if (t === 'module') {
    return (s as { type: 'module'; moduleName: string }).moduleName === moduleName
  }

  if (t === 'category') {
    const sel = s as { type: 'category'; moduleName: string; categoryName: string }
    return sel.moduleName === moduleName && sel.categoryName === categoryName
  }

  if (t === 'node') {
    const sel = s as { type: 'node'; moduleName: string; categoryName: string; nodeId: string }
    return (
      sel.moduleName === moduleName && sel.categoryName === categoryName && sel.nodeId === nodeId
    )
  }

  return false
}

function isModuleExpanded(moduleName: string) {
  return props.expandedModules?.[moduleName] ?? true
}

function toggleModuleExpand(moduleName: string) {
  emit('toggle:module', moduleName)
}

function isCategoryExpanded(moduleName: string, categoryName: string) {
  return props.expandedCategories?.[moduleName]?.[categoryName] ?? true
}

function toggleCategoryExpand(moduleName: string, categoryName: string) {
  emit('toggle:category', moduleName, categoryName)
}
</script>

<style scoped></style>
