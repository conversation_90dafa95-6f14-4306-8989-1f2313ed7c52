<template>
  <div class="space-y-6">
    <div class="space-y-2">
      <h3 class="text-sm font-medium text-muted-foreground">节点基本信息</h3>
      <Separator />
    </div>
    <div class="grid grid-cols-12 gap-4">
      <div class="col-span-12 md:col-span-6">
        <div class="space-y-2">
          <label class="text-sm font-medium leading-none">所属模块</label>
          <Input :model-value="moduleName" disabled />
        </div>
      </div>

      <div class="col-span-12 md:col-span-6">
        <div class="space-y-2">
          <label class="text-sm font-medium leading-none">所属分类</label>
          <Select v-model="formData.category">
            <SelectTrigger>
              <SelectValue placeholder="选择分类" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem v-for="c in categories" :key="c.name" :value="c.name">{{
                c.name
              }}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div class="col-span-12 md:col-span-6">
        <div class="space-y-2">
          <label class="text-sm font-medium leading-none">节点显示名</label>
          <Input v-model="formData.label" placeholder="label" />
        </div>
      </div>

      <div class="col-span-12 md:col-span-6">
        <div class="space-y-2">
          <label class="text-sm font-medium leading-none">节点类型标识</label>
          <Input v-model="formData.type" placeholder="type" />
        </div>
      </div>

      <div class="col-span-12 md:col-span-6">
        <div class="space-y-2">
          <label class="text-sm font-medium leading-none">节点类型（Basic/Compute/Data）</label>
          <Select v-model="formData.nodeType">
            <SelectTrigger>
              <SelectValue placeholder="选择节点类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Basic">Basic</SelectItem>
              <SelectItem value="Compute">Compute</SelectItem>
              <SelectItem value="Data">Data</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div class="col-span-12 md:col-span-6">
        <div class="space-y-2">
          <label class="text-sm font-medium leading-none">排序</label>
          <Input v-model.number="formData.sort" type="number" placeholder="0" />
        </div>
      </div>

      <div class="col-span-12">
        <div class="space-y-2">
          <label class="text-sm font-medium leading-none">描述</label>
          <Textarea v-model="formData.description" rows="3" placeholder="描述..." />
        </div>
      </div>
    </div>

    <Separator />

    <div class="space-y-2">
      <h3 class="text-sm font-medium text-muted-foreground">图标与数据结构</h3>
      <Separator />
    </div>
    <div class="grid grid-cols-12 gap-4">
      <div class="col-span-12 md:col-span-6">
        <div class="space-y-2">
          <label class="text-sm font-medium leading-none">图标类型</label>
          <Select v-model="formData.iconType">
            <SelectTrigger>
              <SelectValue placeholder="选择图标类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="icon">Icon</SelectItem>
              <SelectItem value="svg">SVG</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div class="col-span-12 md:col-span-6">
        <div class="space-y-2">
          <label class="text-sm font-medium leading-none">图标值</label>
          <Input v-model="formData.iconValue" placeholder="如 Battery / energy" />
        </div>
      </div>

      <div class="col-span-12 md:col-span-6">
        <div class="space-y-2">
          <label class="text-sm font-medium leading-none">输入类型</label>
          <TagsInput v-model="formData.inputTypes">
            <TagsInputItem v-for="item in formData.inputTypes" :key="item" :value="item">
              <TagsInputItemText />
              <TagsInputItemDelete />
            </TagsInputItem>
            <TagsInputInput placeholder="输入后回车添加" />
          </TagsInput>
        </div>
      </div>

      <div class="col-span-12 md:col-span-6">
        <div class="space-y-2">
          <label class="text-sm font-medium leading-none">输出类型</label>
          <TagsInput v-model="formData.outputTypes">
            <TagsInputItem v-for="item in formData.outputTypes" :key="item" :value="item">
              <TagsInputItemText />
              <TagsInputItemDelete />
            </TagsInputItem>
            <TagsInputInput placeholder="输入后回车添加" />
          </TagsInput>
        </div>
      </div>

      <div class="col-span-12">
        <div class="space-y-2">
          <label class="text-sm font-medium leading-none">参数(JSON)</label>
          <Textarea v-model="formData.paramsJson" rows="4" placeholder='{ "thickness": 0 }' />
        </div>
      </div>
    </div>

    <div v-if="!props.hideActions" class="flex justify-between">
      <Button type="button" variant="destructive" @click="emit('remove')">删除节点</Button>
      <div class="flex gap-2">
        <Button type="button" variant="outline" @click="onReset">重置</Button>
        <Button type="button" @click="onSave">保存</Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Separator,
  TagsInput,
  TagsInputInput,
  TagsInputItem,
  TagsInputItemDelete,
  TagsInputItemText,
  Textarea,
} from '@mattverse/mattverse-ui'

const props = withDefaults(
  defineProps<{
    moduleName: string
    categories: Array<{ name: string }>
    hideActions?: boolean
  }>(),
  {
    hideActions: false,
  }
)

const emit = defineEmits<{
  save: []
  reset: []
  remove: []
}>()

const model = defineModel<{
  category: string
  label: string
  type: string
  nodeType: 'Basic' | 'Compute' | 'Data'
  sort?: number
  description: string
  iconType: 'svg' | 'icon'
  iconValue: string
  inputTypes: string[]
  outputTypes: string[]
  paramsJson: string
}>()

// 表单数据
const formData = reactive({
  category: '',
  label: '',
  type: '',
  nodeType: 'Basic' as 'Basic' | 'Compute' | 'Data',
  sort: 0 as number,
  description: '',
  iconType: 'svg' as 'svg' | 'icon',
  iconValue: '',
  inputTypes: [] as string[],
  outputTypes: [] as string[],
  paramsJson: '{}',
})

// 监听 model 变化，同步到表单数据
watch(
  () => model.value,
  newModel => {
    if (newModel) {
      formData.category = newModel.category || ''
      formData.label = newModel.label || ''
      formData.type = newModel.type || ''
      formData.nodeType = newModel.nodeType || 'Basic'
      formData.sort = typeof newModel.sort === 'number' ? newModel.sort : 0
      formData.description = newModel.description || ''
      formData.iconType = newModel.iconType || 'svg'
      formData.iconValue = newModel.iconValue || ''
      formData.inputTypes = newModel.inputTypes || []
      formData.outputTypes = newModel.outputTypes || []
      formData.paramsJson = newModel.paramsJson || '{}'
    }
  },
  { deep: true, immediate: true }
)

// 监听表单数据变化，同步到 model
watch(
  formData,
  newFormData => {
    if (model.value) {
      Object.assign(model.value, newFormData)
    }
  },
  { deep: true }
)

function onSave() {
  emit('save')
}

const onReset = () => {
  if (model.value) {
    Object.assign(formData, model.value)
  }
  emit('reset')
}
</script>

<style scoped></style>
