<template>
  <Dialog :open="open" @update:open="emit('update:open', $event)">
    <DialogContent class="max-w-6xl max-h-[90vh] overflow-hidden">
      <DialogHeader>
        <DialogTitle>编辑工具模块</DialogTitle>
        <DialogDescription>
          管理与编辑模块、分类与节点。左侧为结构树，右侧为详情表单。
        </DialogDescription>
      </DialogHeader>

      <div class="flex items-center gap-2 pb-3">
        <div class="flex-1">
          <Input v-model="search" placeholder="搜索模块/分类/节点..." />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <Button variant="outline" class="flex items-center gap-2">
              <MattIcon name="Plus" class="w-4 h-4" />
              新增
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" class="w-40">
            <DropdownMenuItem @click="openAdd('module')">新增模块</DropdownMenuItem>
            <DropdownMenuItem @click="openAdd('category')" :disabled="!currentModuleName">
              新增分类
            </DropdownMenuItem>
            <DropdownMenuItem @click="openAdd('node')" :disabled="!currentModuleName">
              新增节点
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div class="grid grid-cols-[300px_1fr] gap-4 h-[70vh]">
        <!-- 左侧：Tree -->
        <div class="border rounded-lg bg-card">
          <ScrollArea class="h-[calc(70vh-2rem)] p-2">
            <ModuleEditorTree
              :entries="filteredModules"
              :selected="selection"
              :expanded-modules="expandedModules"
              :expanded-categories="expandedCategories"
              @select:module="selectModule"
              @select:category="selectCategory"
              @select:node="selectNode"
              @toggle:module="toggleModuleExpand"
              @toggle:category="toggleCategoryExpand"
            />
          </ScrollArea>
        </div>

        <!-- 右侧：表单 -->
        <div class="border rounded-lg bg-card flex flex-col relative">
          <div
            v-if="!selection.type"
            class="flex-1 flex items-center justify-center text-muted-foreground"
          >
            请选择左侧树中的模块/分类/节点
          </div>

          <!-- 模块表单 -->
          <template v-else-if="selection.type === 'module'">
            <div class="overflow-y-auto p-4" style="height: calc(100% - 100px)">
              <ModuleForm
                v-model="moduleForm"
                @reset="resetModuleForm"
                @save="saveModule"
                :hide-actions="true"
              />
            </div>
            <div class="absolute bottom-0 left-0 right-0 border-t p-4 bg-muted/30 h-[100px]">
              <div class="flex justify-between items-center h-full">
                <Button
                  v-if="!currentModuleBuiltin"
                  type="button"
                  variant="destructive"
                  @click="removeModule"
                >
                  删除模块
                </Button>
                <div v-else></div>
                <div class="flex gap-2">
                  <Button type="button" variant="outline" @click="resetModuleForm"> 重置 </Button>
                  <Button type="button" @click="saveModule"> 保存 </Button>
                </div>
              </div>
            </div>
          </template>

          <!-- 分类表单 -->
          <template v-else-if="selection.type === 'category'">
            <div class="overflow-y-auto p-4" style="height: calc(100% - 100px)">
              <CategoryForm
                :module-name="categoryModuleName"
                v-model="categoryForm"
                @reset="resetCategoryForm"
                @save="saveCategory"
                @remove="removeCategory"
                :hide-actions="true"
              />
            </div>
            <div class="absolute bottom-0 left-0 right-0 border-t p-4 bg-muted/30 h-[100px]">
              <div class="flex justify-between items-center h-full">
                <Button type="button" variant="destructive" @click="removeCategory">
                  删除分类
                </Button>
                <div class="flex gap-2">
                  <Button type="button" variant="outline" @click="resetCategoryForm"> 重置 </Button>
                  <Button type="button" @click="saveCategory"> 保存 </Button>
                </div>
              </div>
            </div>
          </template>

          <!-- 节点表单 -->
          <template v-else-if="selection.type === 'node'">
            <div class="overflow-y-auto p-4" style="height: calc(100% - 100px)">
              <NodeForm
                :key="nodeKey"
                :module-name="nodeModuleName"
                :categories="nodeCategories"
                :model-value="nodeForm"
                @update:model-value="updateNodeForm"
                @reset="resetNodeForm"
                @save="saveNode"
                @remove="removeNode"
                :hide-actions="true"
              />
            </div>
            <div class="absolute bottom-0 left-0 right-0 border-t p-4 bg-muted/30 h-[100px]">
              <div class="flex justify-between items-center h-full">
                <Button type="button" variant="destructive" @click="removeNode"> 删除节点 </Button>
                <div class="flex gap-2">
                  <Button type="button" variant="outline" @click="resetNodeForm"> 重置 </Button>
                  <Button type="button" @click="saveNode"> 保存 </Button>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 去掉底部关闭按钮，遵循右上角 X 关闭 -->

      <!-- 新增弹框 -->
      <Dialog :open="addDialog.open" @update:open="v => (addDialog.open = v)">
        <DialogContent class="max-w-xl">
          <DialogHeader>
            <DialogTitle>{{ addDialog.title }}</DialogTitle>
          </DialogHeader>
          <form class="space-y-4" @submit.prevent="confirmAdd">
            <template v-if="addDialog.kind === 'module'">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <Label>模块名称</Label>
                  <Input v-model="addModuleForm.name" required />
                </div>
                <div>
                  <Label>类型</Label>
                  <Input v-model="addModuleForm.type" required />
                </div>
                <div class="col-span-2">
                  <Label>描述</Label>
                  <Textarea v-model="addModuleForm.description" rows="3" />
                </div>
                <div>
                  <Label>图标类型</Label>
                  <Select v-model="addModuleForm.iconType">
                    <SelectTrigger>
                      <SelectValue placeholder="选择图标类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="icon">Icon</SelectItem>
                      <SelectItem value="svg">SVG</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>图标值</Label>
                  <Input v-model="addModuleForm.iconValue" />
                </div>
                <div class="flex items-center gap-3 pt-6">
                  <Switch
                    :model-value="addModuleForm.enabled"
                    @update:model-value="v => (addModuleForm.enabled = v)"
                  />
                  <span class="text-sm">是否启用</span>
                </div>
              </div>
            </template>

            <template v-else-if="addDialog.kind === 'category'">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <Label>所属模块</Label>
                  <Select v-model="addCategoryForm.module">
                    <SelectTrigger>
                      <SelectValue placeholder="选择模块" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="m in moduleNames" :key="m" :value="m">{{ m }}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>分类名称</Label>
                  <Input v-model="addCategoryForm.name" required />
                </div>
                <div>
                  <Label>排序</Label>
                  <Input type="number" v-model.number="addCategoryForm.sort" />
                </div>
              </div>
            </template>

            <template v-else-if="addDialog.kind === 'node'">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <Label>所属模块</Label>
                  <Select v-model="addNodeForm.module">
                    <SelectTrigger>
                      <SelectValue placeholder="选择模块" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="m in moduleNames" :key="m" :value="m">{{ m }}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>所属分类</Label>
                  <Select v-model="addNodeForm.category">
                    <SelectTrigger>
                      <SelectValue placeholder="选择分类" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem
                        v-for="c in moduleCategories(addNodeForm.module)"
                        :key="c.name"
                        :value="c.name"
                      >
                        {{ c.name }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>节点显示名</Label>
                  <Input v-model="addNodeForm.label" />
                </div>
                <div>
                  <Label>节点类型标识</Label>
                  <Input v-model="addNodeForm.type" />
                </div>
                <div>
                  <Label>节点类型</Label>
                  <Select v-model="addNodeForm.nodeType">
                    <SelectTrigger>
                      <SelectValue placeholder="选择节点类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Basic">Basic</SelectItem>
                      <SelectItem value="Compute">Compute</SelectItem>
                      <SelectItem value="Data">Data</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>排序</Label>
                  <Input type="number" v-model.number="addNodeForm.sort" />
                </div>
                <div class="col-span-2">
                  <Label>描述</Label>
                  <Textarea v-model="addNodeForm.description" rows="3" />
                </div>
                <div>
                  <Label>图标类型</Label>
                  <Select v-model="addNodeForm.iconType">
                    <SelectTrigger>
                      <SelectValue placeholder="选择图标类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="icon">Icon</SelectItem>
                      <SelectItem value="svg">SVG</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>图标值</Label>
                  <Input v-model="addNodeForm.iconValue" />
                </div>
                <div>
                  <Label>输入类型</Label>
                  <TagsInput v-model="addNodeForm.inputTypes">
                    <TagsInputItem v-for="item in addNodeForm.inputTypes" :key="item" :value="item">
                      <TagsInputItemText />
                      <TagsInputItemDelete />
                    </TagsInputItem>
                    <TagsInputInput placeholder="输入后回车添加" />
                  </TagsInput>
                </div>
                <div>
                  <Label>输出类型</Label>
                  <TagsInput v-model="addNodeForm.outputTypes">
                    <TagsInputItem
                      v-for="item in addNodeForm.outputTypes"
                      :key="item"
                      :value="item"
                    >
                      <TagsInputItemText />
                      <TagsInputItemDelete />
                    </TagsInputItem>
                    <TagsInputInput placeholder="输入后回车添加" />
                  </TagsInput>
                </div>
                <div class="col-span-2">
                  <Label>参数(JSON)</Label>
                  <Textarea v-model="addNodeForm.paramsJson" rows="4" />
                </div>
              </div>
            </template>

            <DialogFooter>
              <Button type="button" variant="outline" @click="addDialog.open = false">取消</Button>
              <Button type="submit">确认</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useNodeModulesStore } from '@/store'
import { toast } from 'vue-sonner'
// shadcn-vue tags input
import { ModuleEditorTree, ModuleForm, CategoryForm, NodeForm } from './index'

defineProps<{ open: boolean }>()
const emit = defineEmits<{ 'update:open': [v: boolean] }>()

const toolsStore = useNodeModulesStore()
const { getAllModules } = storeToRefs(toolsStore)

const search = ref('')

const filteredModules = computed(() => {
  const entries = Object.entries(getAllModules.value)
  if (!search.value.trim()) return entries
  const q = search.value.toLowerCase()
  return entries
    .map(([k, m]) => {
      const matchModule =
        m.name.toLowerCase().includes(q) || (m.type || '').toLowerCase().includes(q)
      const categories = (m.categories || [])
        .map(c => ({
          ...c,
          nodes: c.nodes.filter(n => {
            const label = (n.data?.label || '').toLowerCase()
            const type = (n.type || '').toLowerCase()
            const nodeType = (n.data?.nodeType || '').toLowerCase()
            return label.includes(q) || type.includes(q) || nodeType.includes(q)
          }),
        }))
        .filter(c => c.name.toLowerCase().includes(q) || c.nodes.length > 0)
      if (matchModule || categories.length > 0) {
        return [k, { ...m, categories }] as (typeof entries)[number]
      }
      return null
    })
    .filter(Boolean) as typeof entries
})

const moduleNames = computed(() => Object.keys(getAllModules.value))

type Selection =
  | { type: null }
  | { type: 'module'; moduleName: string }
  | { type: 'category'; moduleName: string; categoryName: string }
  | { type: 'node'; moduleName: string; categoryName: string; nodeId: string }

const selection = ref<Selection>({ type: null })

// 辅助函数：安全获取选择状态

function getNodeSelection() {
  const sel = selection.value
  return sel.type === 'node'
    ? (sel as { type: 'node'; moduleName: string; categoryName: string; nodeId: string })
    : null
}

const currentModuleName = computed(() => {
  const sel = selection.value
  if (sel.type === 'module') return (sel as { type: 'module'; moduleName: string }).moduleName
  if (sel.type === 'category')
    return (sel as { type: 'category'; moduleName: string; categoryName: string }).moduleName
  if (sel.type === 'node')
    return (sel as { type: 'node'; moduleName: string; categoryName: string; nodeId: string })
      .moduleName
  return ''
})

const currentModuleBuiltin = computed(() => {
  if (selection.value.type !== 'module') return false
  const sel = selection.value as { type: 'module'; moduleName: string }
  const mod = getAllModules.value[sel.moduleName]
  return !!mod?.isBuiltin
})

// 计算属性用于模板中的类型安全访问
const categoryModuleName = computed(() => {
  if (selection.value.type === 'category') {
    return (selection.value as { type: 'category'; moduleName: string; categoryName: string })
      .moduleName
  }
  return ''
})

const nodeModuleName = computed(() => {
  if (selection.value.type === 'node') {
    return (
      selection.value as { type: 'node'; moduleName: string; categoryName: string; nodeId: string }
    ).moduleName
  }
  return ''
})

const nodeCategories = computed(() => {
  if (selection.value.type === 'node') {
    const moduleName = (
      selection.value as { type: 'node'; moduleName: string; categoryName: string; nodeId: string }
    ).moduleName
    return moduleCategories(moduleName)
  }
  return []
})

const nodeKey = computed(() => {
  if (selection.value.type === 'node') {
    return `node-${(selection.value as { type: 'node'; moduleName: string; categoryName: string; nodeId: string }).nodeId}`
  }
  return 'node-empty'
})

// 左侧树展开状态
const expandedModules = ref<Record<string, boolean>>({})
const expandedCategories = ref<Record<string, Record<string, boolean>>>({})

function isModuleExpanded(moduleName: string) {
  return expandedModules.value[moduleName] ?? true
}
function toggleModuleExpand(moduleName: string) {
  expandedModules.value[moduleName] = !isModuleExpanded(moduleName)
}
function isCategoryExpanded(moduleName: string, categoryName: string) {
  if (!expandedCategories.value[moduleName]) expandedCategories.value[moduleName] = {}
  const map = expandedCategories.value[moduleName]
  return map[categoryName] ?? true
}
function toggleCategoryExpand(moduleName: string, categoryName: string) {
  if (!expandedCategories.value[moduleName]) expandedCategories.value[moduleName] = {}
  const map = expandedCategories.value[moduleName]
  map[categoryName] = !isCategoryExpanded(moduleName, categoryName)
}

function selectModule(moduleName: string) {
  nextTick(() => {
    selection.value = { type: 'module', moduleName }
  })
}
function selectCategory(moduleName: string, categoryName: string) {
  nextTick(() => {
    selection.value = { type: 'category', moduleName, categoryName }
  })
}
function selectNode(moduleName: string, categoryName: string, nodeId: string) {
  nextTick(() => {
    selection.value = { type: 'node', moduleName, categoryName, nodeId }
  })
}

// 表单状态 - 模块
const moduleForm = reactive({
  name: '',
  type: '',
  description: '',
  sort: 999 as number | undefined,
  enabled: true,
  iconType: 'svg' as 'svg' | 'icon',
  iconValue: '',
})

watch(selection, () => {
  if (selection.value.type === 'module') {
    const sel = selection.value as { type: 'module'; moduleName: string }
    const mod = getAllModules.value[sel.moduleName]
    if (mod) {
      moduleForm.name = mod.name
      moduleForm.type = mod.type
      moduleForm.description = mod.description || ''
      moduleForm.sort = typeof mod.sort === 'number' ? mod.sort : undefined
      moduleForm.enabled = !!mod.enabled
      const icon = typeof mod.icon === 'string' ? { type: 'icon', value: mod.icon } : mod.icon
      moduleForm.iconType = (icon?.type === 'svg' ? 'svg' : 'icon') as 'svg' | 'icon'
      moduleForm.iconValue = icon?.value || ''
    }
  }
})

function resetModuleForm() {
  if (selection.value.type !== 'module') return
  const sel = selection.value as { type: 'module'; moduleName: string }
  const mod = getAllModules.value[sel.moduleName]
  if (!mod) return
  moduleForm.name = mod.name
  moduleForm.type = mod.type
  moduleForm.description = mod.description || ''
  moduleForm.sort = typeof mod.sort === 'number' ? mod.sort : undefined
  moduleForm.enabled = !!mod.enabled
  const icon = typeof mod.icon === 'string' ? { type: 'icon', value: mod.icon } : mod.icon
  moduleForm.iconType = (icon?.type === 'svg' ? 'svg' : 'icon') as 'svg' | 'icon'
  moduleForm.iconValue = icon?.value || ''
}

function saveModule() {
  if (selection.value.type !== 'module') return
  const sel = selection.value as { type: 'module'; moduleName: string }
  const oldName = sel.moduleName
  const ok = toolsStore.updateModule(oldName, {
    name: moduleForm.name,
    type: moduleForm.type,
    description: moduleForm.description,
    sort: moduleForm.sort,
    enabled: moduleForm.enabled,
    icon: { type: moduleForm.iconType, value: moduleForm.iconValue },
  })
  if (!ok) {
    toast.error('保存失败', { description: '可能存在同名模块或数据无效' })
    return
  }
  // 若重命名，更新当前选择
  if (oldName !== moduleForm.name) {
    selection.value = { type: 'module', moduleName: moduleForm.name }
  }
  toast.success('已保存模块')
}

function removeModule() {
  if (selection.value.type !== 'module') return
  const sel = selection.value as { type: 'module'; moduleName: string }
  const ok = toolsStore.deleteModule(sel.moduleName)
  if (!ok) {
    toast.error('删除失败', { description: '内置模块不可删除或模块不存在' })
    return
  }
  selection.value = { type: null }
  toast.success('已删除模块')
}

// 表单状态 - 分类
const categoryForm = reactive({ name: '', sort: undefined as number | undefined })

watch(selection, () => {
  if (selection.value.type === 'category') {
    const sel = selection.value as { type: 'category'; moduleName: string; categoryName: string }
    const mod = getAllModules.value[sel.moduleName]
    const cat = mod?.categories?.find(c => c.name === sel.categoryName)
    if (cat) {
      categoryForm.name = cat.name
      categoryForm.sort = typeof cat.sort === 'number' ? cat.sort : undefined
    }
  }
})

function resetCategoryForm() {
  if (selection.value.type !== 'category') return
  const sel = selection.value as { type: 'category'; moduleName: string; categoryName: string }
  const mod = getAllModules.value[sel.moduleName]
  const cat = mod?.categories?.find(c => c.name === sel.categoryName)
  if (!cat) return
  categoryForm.name = cat.name
  categoryForm.sort = typeof cat.sort === 'number' ? cat.sort : undefined
}

function saveCategory() {
  if (selection.value.type !== 'category') return
  const sel = selection.value as { type: 'category'; moduleName: string; categoryName: string }
  const { moduleName, categoryName } = sel
  const ok = toolsStore.updateCategory(moduleName, categoryName, {
    name: categoryForm.name,
    sort: categoryForm.sort,
  })
  if (!ok) {
    toast.error('保存失败', { description: '同名分类已存在或数据无效' })
    return
  }
  if (categoryName !== categoryForm.name) {
    selection.value = { type: 'category', moduleName, categoryName: categoryForm.name }
  }
  toast.success('已保存分类')
}

function removeCategory() {
  if (selection.value.type !== 'category') return
  const sel = selection.value as { type: 'category'; moduleName: string; categoryName: string }
  const { moduleName, categoryName } = sel
  const ok = toolsStore.deleteCategory(moduleName, categoryName)
  if (!ok) {
    toast.error('删除失败')
    return
  }
  selection.value = { type: 'module', moduleName }
  toast.success('已删除分类')
}

// 表单状态 - 节点
const nodeForm = ref({
  category: '',
  label: '',
  type: '',
  nodeType: 'Basic' as 'Basic' | 'Compute' | 'Data',
  sort: 0 as number,
  description: '',
  iconType: 'svg' as 'svg' | 'icon',
  iconValue: '',
  inputTypes: [] as string[],
  outputTypes: [] as string[],
  paramsJson: '{}',
})

watch(selection, () => {
  const nodeSel = getNodeSelection()

  if (nodeSel) {
    const mod = getAllModules.value[nodeSel.moduleName]
    const cat = mod?.categories?.find(c => c.name === nodeSel.categoryName)
    const node = cat?.nodes.find(n => n.id === nodeSel.nodeId)

    if (node && node.data) {
      // 正确映射节点数据到表单数据
      nodeForm.value.category = nodeSel.categoryName
      nodeForm.value.label = node.data.label || ''
      nodeForm.value.type = node.data.type || node.type || ''
      nodeForm.value.nodeType = (node.data.nodeType as any) || 'Basic'
      nodeForm.value.sort =
        typeof (node as any).sort === 'number'
          ? (node as any).sort
          : typeof node.data.sort === 'number'
            ? node.data.sort
            : 0
      nodeForm.value.description = node.data.description || ''

      // 处理图标数据
      const icon = node.data.icon
      if (icon && typeof icon === 'object' && icon.type && icon.value) {
        nodeForm.value.iconType = (icon.type === 'svg' ? 'svg' : 'icon') as 'svg' | 'icon'
        nodeForm.value.iconValue = icon.value
      } else {
        nodeForm.value.iconType = 'svg'
        nodeForm.value.iconValue = ''
      }

      // 处理输入输出类型
      nodeForm.value.inputTypes = Array.isArray(node.data.inputType) ? [...node.data.inputType] : []
      nodeForm.value.outputTypes = Array.isArray(node.data.outputType)
        ? [...node.data.outputType]
        : []

      // 处理参数JSON
      try {
        nodeForm.value.paramsJson = JSON.stringify(node.data.params ?? {}, null, 2)
      } catch {
        nodeForm.value.paramsJson = '{}'
      }
    } else {
      // Node not found in data
    }
  } else {
    // 当不是节点选择时，重置节点表单
    nodeForm.value.category = ''
    nodeForm.value.label = ''
    nodeForm.value.type = ''
    nodeForm.value.nodeType = 'Basic'
    nodeForm.value.sort = undefined
    nodeForm.value.description = ''
    nodeForm.value.iconType = 'svg'
    nodeForm.value.iconValue = ''
    nodeForm.value.inputTypes = []
    nodeForm.value.outputTypes = []
    nodeForm.value.paramsJson = '{}'
  }
})

function updateNodeForm(newValue: any) {
  nodeForm.value = { ...nodeForm.value, ...newValue }
}

function resetNodeForm() {
  const nodeSel = getNodeSelection()
  if (!nodeSel) return
  const mod = getAllModules.value[nodeSel.moduleName]
  const cat = mod?.categories?.find(c => c.name === nodeSel.categoryName)
  const node = cat?.nodes.find(n => n.id === nodeSel.nodeId)
  if (!node || !node.data) return

  // 正确映射节点数据到表单数据
  nodeForm.value.category = nodeSel.categoryName
  nodeForm.value.label = node.data.label || ''
  nodeForm.value.type = node.data.type || node.type || ''
  nodeForm.value.nodeType = (node.data.nodeType as any) || 'Basic'
  nodeForm.value.sort =
    typeof (node as any).sort === 'number'
      ? (node as any).sort
      : typeof node.data.sort === 'number'
        ? node.data.sort
        : 0
  nodeForm.value.description = node.data.description || ''

  // 处理图标数据
  const icon = node.data.icon
  if (icon && typeof icon === 'object' && icon.type && icon.value) {
    nodeForm.value.iconType = (icon.type === 'svg' ? 'svg' : 'icon') as 'svg' | 'icon'
    nodeForm.value.iconValue = icon.value
  } else {
    nodeForm.value.iconType = 'svg'
    nodeForm.value.iconValue = ''
  }

  // 处理输入输出类型
  nodeForm.value.inputTypes = Array.isArray(node.data.inputType) ? [...node.data.inputType] : []
  nodeForm.value.outputTypes = Array.isArray(node.data.outputType) ? [...node.data.outputType] : []

  // 处理参数JSON
  try {
    nodeForm.value.paramsJson = JSON.stringify(node.data.params ?? {}, null, 2)
  } catch {
    nodeForm.value.paramsJson = '{}'
  }
}

function moduleCategories(moduleName?: string) {
  if (!moduleName) return [] as Array<{ name: string }>
  const mod = getAllModules.value[moduleName]
  return mod?.categories || []
}

function saveNode() {
  const nodeSel = getNodeSelection()
  if (!nodeSel) return
  let params: any = {}
  if (nodeForm.value.paramsJson.trim()) {
    try {
      params = JSON.parse(nodeForm.value.paramsJson)
    } catch {
      toast.error('保存失败', { description: '参数 JSON 解析失败' })
      return
    }
  }
  const ok = toolsStore.updateNode(nodeSel.moduleName, nodeSel.nodeId, {
    type: nodeForm.value.type,
    data: {
      label: nodeForm.value.label,
      type: nodeForm.value.type,
      icon: { type: nodeForm.value.iconType, value: nodeForm.value.iconValue } as any,
      description: nodeForm.value.description,
      category: nodeForm.value.category,
      nodeType: nodeForm.value.nodeType,
      inputType: nodeForm.value.inputTypes,
      outputType: nodeForm.value.outputTypes,
      params,
      sort: nodeForm.value.sort,
    },
    moveToCategory: nodeForm.value.category,
  })
  if (!ok) {
    toast.error('保存失败')
    return
  }
  selection.value = {
    type: 'node',
    moduleName: nodeSel.moduleName,
    categoryName: nodeForm.value.category,
    nodeId: nodeSel.nodeId,
  }
  toast.success('已保存节点')
}

function removeNode() {
  const nodeSel = getNodeSelection()
  if (!nodeSel) return
  const { moduleName, categoryName } = nodeSel
  const ok = toolsStore.deleteNode(moduleName, nodeSel.nodeId)
  if (!ok) {
    toast.error('删除失败')
    return
  }
  selection.value = { type: 'category', moduleName, categoryName }
  toast.success('已删除节点')
}

// 新增相关
const addDialog = reactive({
  open: false,
  kind: null as null | 'module' | 'category' | 'node',
  title: '',
})

function openAdd(kind: 'module' | 'category' | 'node') {
  addDialog.kind = kind
  addDialog.open = true
  addDialog.title = kind === 'module' ? '新增模块' : kind === 'category' ? '新增分类' : '新增节点'
  // 初始化默认值
  addModuleForm.name = ''
  addModuleForm.type = ''
  addModuleForm.description = ''
  addModuleForm.iconType = 'svg'
  addModuleForm.iconValue = ''
  addModuleForm.enabled = true

  addCategoryForm.module = currentModuleName.value || moduleNames.value[0] || ''
  addCategoryForm.name = ''
  addCategoryForm.sort = undefined

  addNodeForm.module = currentModuleName.value || moduleNames.value[0] || ''
  addNodeForm.category = moduleCategories(addNodeForm.module)[0]?.name || ''
  addNodeForm.label = ''
  addNodeForm.type = ''
  addNodeForm.nodeType = 'Basic'
  addNodeForm.sort = 0
  addNodeForm.description = ''
  addNodeForm.iconType = 'svg'
  addNodeForm.iconValue = ''
  addNodeForm.inputTypes = []
  addNodeForm.outputTypes = []
  addNodeForm.paramsJson = '{}'
}

const addModuleForm = reactive({
  name: '',
  type: '',
  description: '',
  iconType: 'svg' as 'svg' | 'icon',
  iconValue: '',
  enabled: true,
})

const addCategoryForm = reactive({ module: '', name: '', sort: undefined as number | undefined })

const addNodeForm = reactive({
  module: '',
  category: '',
  label: '',
  type: '',
  nodeType: 'Basic',
  sort: 0 as number,
  description: '',
  iconType: 'svg' as 'svg' | 'icon',
  iconValue: '',
  inputTypes: [] as string[],
  outputTypes: [] as string[],
  paramsJson: '{}',
})

function confirmAdd() {
  if (addDialog.kind === 'module') {
    toolsStore.addModule(addModuleForm.name, {
      name: addModuleForm.name,
      type: addModuleForm.type,
      description: addModuleForm.description,
      sort: 999,
      categories: [],
      icon: { type: addModuleForm.iconType, value: addModuleForm.iconValue } as any,
      enabled: addModuleForm.enabled,
      isBuiltin: false,
    } as any)
    toast.success('已新增模块')
    selection.value = { type: 'module', moduleName: addModuleForm.name }
  } else if (addDialog.kind === 'category') {
    if (!addCategoryForm.module) {
      toast.error('请选择所属模块')
      return
    }
    const ok = toolsStore.addCategory(addCategoryForm.module, {
      name: addCategoryForm.name,
      sort: addCategoryForm.sort,
    })
    if (!ok) {
      toast.error('新增失败', { description: '分类重名或模块不存在' })
      return
    }
    toast.success('已新增分类')
    selection.value = {
      type: 'category',
      moduleName: addCategoryForm.module,
      categoryName: addCategoryForm.name,
    }
  } else if (addDialog.kind === 'node') {
    if (!addNodeForm.module || !addNodeForm.category) {
      toast.error('请选择模块与分类')
      return
    }
    let params: any = {}
    try {
      params = addNodeForm.paramsJson.trim() ? JSON.parse(addNodeForm.paramsJson) : {}
    } catch {
      toast.error('参数 JSON 解析失败')
      return
    }
    const ok = toolsStore.addNode(addNodeForm.module, addNodeForm.category, {
      type: addNodeForm.type,
      data: {
        label: addNodeForm.label,
        type: addNodeForm.type,
        icon: { type: addNodeForm.iconType, value: addNodeForm.iconValue } as any,
        description: addNodeForm.description,
        category: addNodeForm.category,
        nodeType: addNodeForm.nodeType,
        inputType: addNodeForm.inputTypes,
        outputType: addNodeForm.outputTypes,
        params,
        sort: addNodeForm.sort,
      },
    })
    if (!ok) {
      toast.error('新增失败')
      return
    }
    toast.success('已新增节点')
    // 选中到新分类下，节点定位无法获知 id，这里保持在分类上
    selection.value = {
      type: 'category',
      moduleName: addNodeForm.module,
      categoryName: addNodeForm.category,
    }
  }
  addDialog.open = false
}
</script>

<style scoped></style>
