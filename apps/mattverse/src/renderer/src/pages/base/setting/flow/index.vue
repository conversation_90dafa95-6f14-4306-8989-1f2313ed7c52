<template>
  <div class="flex flex-col h-[calc(100vh-3rem)] overflow-hidden">
    <!-- 页面标题和操作按钮 -->
    <div class="flex-shrink-0 px-6 py-4 border-0 bg-background">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold tracking-tight">{{ $t('settings.flow.title') }}</h1>
          <p class="text-muted-foreground text-sm mt-1">
            {{ $t('settings.flow.description') }}
          </p>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-center space-x-2">
          <Button variant="outline" size="sm" @click="exportFlowSettings">
            <MattIcon name="Download" class="mr-2 h-4 w-4" />
            {{ $t('settings.export_settings') }}
          </Button>
          <Button
            v-for="action in actionButtons"
            :key="action.key"
            :variant="action.variant"
            size="sm"
            @click="action.handler"
          >
            <MattIcon :name="action.icon" class="mr-2 h-4 w-4" />
            {{ action.label }}
          </Button>
        </div>
      </div>
    </div>

    <!-- 设置内容 - 可滚动区域 -->
    <div class="flex-1 min-h-0 overflow-y-auto overflow-x-hidden scrollbar">
      <div class="container mx-auto max-w-7xl p-6">
        <div class="columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6">
          <!-- 执行设置 -->
          <ExecutionSettings
            :flow-settings="flowSettings"
            :update-flow-settings="updateFlowSettings"
          />

          <!-- 编辑器设置 -->
          <EditorSettings
            :flow-settings="flowSettings"
            :update-flow-settings="updateFlowSettings"
          />

          <!-- 网格设置 -->
          <FlowGridSettings
            :flow-settings="flowSettings"
            :update-flow-settings="updateFlowSettings"
            :update-grid-settings="updateGridSettings"
            :update-snap-grid="updateSnapGrid"
          />

          <!-- 缩放设置 -->
          <FlowZoomSettings
            :flow-settings="flowSettings"
            :update-flow-settings="updateFlowSettings"
            :update-default-viewport="updateDefaultViewport"
          />

          <!-- 边缘设置 -->
          <EdgeSettings
            :flow-settings="flowSettings"
            :update-edge-config="updateEdgeConfig"
            :update-edge-style="updateEdgeStyle"
            :update-edge-marker-end="updateEdgeMarkerEnd"
          />

          <!-- 背景设置 -->
          <BackgroundSettings
            :flow-settings="flowSettings"
            :update-background-config="updateBackgroundConfig"
          />

          <!-- 界面设置 -->
          <InterfaceSettings
            :flow-settings="flowSettings"
            :update-flow-settings="updateFlowSettings"
            :update-mini-map-config="updateMiniMapConfig"
            :update-animation-config="updateAnimationConfig"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from '@mattverse/i18n'
import { useFlowSettingsStore } from '@/store'
import type {
  GridSettings,
  EdgeConfig,
  SnapGrid,
  DefaultViewport,
  MiniMapConfig,
  BackgroundConfig,
  AnimationConfig,
  FlowSettingsState,
} from '@/store'
import {
  ExecutionSettings,
  EditorSettings,
  GridSettings as FlowGridSettings,
  ZoomSettings as FlowZoomSettings,
  EdgeSettings,
  BackgroundSettings,
  InterfaceSettings,
} from './components'

const { t } = useI18n()
const flowSettingsStore = useFlowSettingsStore()

// 计算属性
const flowSettings = computed(() => flowSettingsStore.settings)

// 操作按钮
const actionButtons = computed(() => [
  {
    key: 'reset',
    variant: 'outline' as const,
    icon: 'RotateCcw',
    label: t('settings.reset_to_defaults'),
    handler: resetFlowSettings,
  },
  {
    key: 'save',
    variant: 'default' as const,
    icon: 'Save',
    label: t('common.save'),
    handler: saveFlowSettings,
  },
])

// 方法
const updateFlowSettings = (key: keyof FlowSettingsState, value: any) => {
  flowSettingsStore.updateSettings({ [key]: value })
}

const updateGridSettings = (key: keyof GridSettings, value: any) => {
  flowSettingsStore.updateGridSettings({ [key]: value })
}

const updateSnapGrid = (key: keyof SnapGrid, value: any) => {
  flowSettingsStore.updateSnapGrid({ [key]: value })
}

const updateDefaultViewport = (key: keyof DefaultViewport, value: any) => {
  flowSettingsStore.updateDefaultViewport({ [key]: value })
}

const updateEdgeConfig = (key: keyof EdgeConfig, value: any) => {
  flowSettingsStore.updateEdgeConfig({ [key]: value })
}

const updateEdgeStyle = (key: keyof EdgeConfig['style'], value: any) => {
  flowSettingsStore.updateEdgeStyle({ [key]: value })
}

const updateEdgeMarkerEnd = (key: keyof EdgeConfig['markerEnd'], value: any) => {
  flowSettingsStore.updateEdgeMarkerEnd({ [key]: value })
}

const updateMiniMapConfig = (key: keyof MiniMapConfig, value: any) => {
  flowSettingsStore.updateMiniMapConfig({ [key]: value })
}

const updateBackgroundConfig = (key: keyof BackgroundConfig, value: any) => {
  flowSettingsStore.updateBackgroundConfig({ [key]: value })
}

const updateAnimationConfig = (key: keyof AnimationConfig, value: any) => {
  flowSettingsStore.updateAnimationConfig({ [key]: value })
}

const exportFlowSettings = () => {
  try {
    const settings = flowSettingsStore.exportSettings()
    const dataStr = JSON.stringify(settings, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })

    const link = document.createElement('a')
    link.href = URL.createObjectURL(dataBlob)
    link.download = `mattverse-flow-settings-${new Date().toISOString().split('T')[0]}.json`
    link.click()

    // 使用通知而不是 console.log
    // TODO: 添加通知服务
  } catch {
    // TODO: 添加错误通知
  }
}

const resetFlowSettings = () => {
  flowSettingsStore.resetToDefaults()
  // TODO: 添加成功通知
}

const saveFlowSettings = () => {
  // 设置会自动持久化，这里只是给用户反馈
  // TODO: 添加保存成功通知
}
</script>
