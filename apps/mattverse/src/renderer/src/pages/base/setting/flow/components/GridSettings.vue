<template>
  <Card
    class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
  >
    <CardHeader>
      <CardTitle class="flex items-center space-x-2">
        <MattIcon name="Grid3x3" class="h-5 w-5" />
        <span>{{ $t('settings.flow.grid.title') }}</span>
      </CardTitle>
      <CardDescription>
        {{ $t('settings.flow.grid.description') }}
      </CardDescription>
    </CardHeader>
    <CardContent class="space-y-4">
      <!-- 启用网格 -->
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{ $t('settings.flow.grid.enabled') }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.flow.grid.enabled_desc') }}
          </p>
        </div>
        <Switch
          :model-value="flowSettings.grid.enabled"
          @update:model-value="updateGridSettings('enabled', $event)"
        />
      </div>

      <Separator />

      <!-- 网格大小 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.grid.size') }}</Label>
        <div class="space-y-2">
          <Slider
            :model-value="[flowSettings.grid.size]"
            :max="50"
            :min="10"
            :step="5"
            @update:model-value="updateGridSettings('size', $event[0])"
          />
          <div class="flex justify-between text-xs text-muted-foreground">
            <span>10px</span>
            <span class="font-medium">{{ flowSettings.grid.size }}px</span>
            <span>50px</span>
          </div>
        </div>
      </div>

      <Separator />

      <!-- 吸附到网格 -->
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{ $t('settings.flow.grid.snap_to_grid') }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.flow.grid.snap_to_grid_desc') }}
          </p>
        </div>
        <Switch
          :model-value="flowSettings.grid.snapToGrid"
          @update:model-value="updateGridSettings('snapToGrid', $event)"
        />
      </div>

      <Separator />

      <!-- 显示网格 -->
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{ $t('settings.flow.grid.show_grid') }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.flow.grid.show_grid_desc') }}
          </p>
        </div>
        <Switch
          :model-value="flowSettings.grid.showGrid"
          @update:model-value="updateGridSettings('showGrid', $event)"
        />
      </div>

      <Separator />

      <!-- 吸附到线条 -->
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{ $t('settings.flow.grid.snap_to_lines') }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.flow.grid.snap_to_lines_desc') }}
          </p>
        </div>
        <Switch
          :model-value="flowSettings.snapToLines"
          @update:model-value="updateFlowSettings('snapToLines', $event)"
        />
      </div>

      <Separator />

      <!-- 吸附网格配置 -->
      <div class="space-y-3">
        <Label class="text-sm">{{ $t('settings.flow.grid.snap_grid_config') }}</Label>
        
        <!-- X 轴间距 -->
        <div class="space-y-2">
          <Label class="text-xs text-muted-foreground">{{ $t('settings.flow.grid.snap_grid_x') }}</Label>
          <div class="space-y-2">
            <Slider
              :model-value="[flowSettings.snapGrid.x]"
              :max="50"
              :min="5"
              :step="5"
              @update:model-value="updateSnapGrid('x', $event[0])"
            />
            <div class="flex justify-between text-xs text-muted-foreground">
              <span>5px</span>
              <span class="font-medium">{{ flowSettings.snapGrid.x }}px</span>
              <span>50px</span>
            </div>
          </div>
        </div>

        <!-- Y 轴间距 -->
        <div class="space-y-2">
          <Label class="text-xs text-muted-foreground">{{ $t('settings.flow.grid.snap_grid_y') }}</Label>
          <div class="space-y-2">
            <Slider
              :model-value="[flowSettings.snapGrid.y]"
              :max="50"
              :min="5"
              :step="5"
              @update:model-value="updateSnapGrid('y', $event[0])"
            />
            <div class="flex justify-between text-xs text-muted-foreground">
              <span>5px</span>
              <span class="font-medium">{{ flowSettings.snapGrid.y }}px</span>
              <span>50px</span>
            </div>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import type { GridSettings, SnapGrid, FlowSettingsState } from '@/store'

interface Props {
  flowSettings: FlowSettingsState
  updateFlowSettings: (key: keyof FlowSettingsState, value: any) => void
  updateGridSettings: (key: keyof GridSettings, value: any) => void
  updateSnapGrid: (key: keyof SnapGrid, value: any) => void
}

defineProps<Props>()
</script>
