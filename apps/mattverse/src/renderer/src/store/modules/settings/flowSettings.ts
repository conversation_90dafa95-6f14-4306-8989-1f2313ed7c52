import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

/**
 * 流程执行模式
 */
export type FlowExecutionMode = 'sequential' | 'parallel' | 'mixed'

/**
 * 节点连接模式
 */
export type NodeConnectionMode = 'auto' | 'manual' | 'smart'

/**
 * 流程验证级别
 */
export type FlowValidationLevel = 'none' | 'basic' | 'strict'

/**
 * 流程布局方向
 */
export type FlowLayoutDirection = 'horizontal' | 'vertical' | 'auto'

/**
 * 网格设置
 */
export interface GridSettings {
  enabled: boolean
  size: number
  snapToGrid: boolean
  showGrid: boolean
}

/**
 * 缩放设置
 */
export interface ZoomSettings {
  minZoom: number
  maxZoom: number
  defaultZoom: number
  zoomStep: number
}

/**
 * 自动保存设置
 */
export interface AutoSaveSettings {
  enabled: boolean
  interval: number // 秒
  maxBackups: number
}

/**
 * 性能设置
 */
export interface PerformanceSettings {
  enableVirtualization: boolean
  maxVisibleNodes: number
  enableLazyLoading: boolean
  renderThrottleMs: number
}

/**
 * 调试设置
 */
export interface DebugSettings {
  showNodeIds: boolean
  showExecutionPath: boolean
  enablePerformanceMonitor: boolean
  logLevel: 'none' | 'error' | 'warn' | 'info' | 'debug'
}

/**
 * 边缘配置
 */
export interface EdgeConfig {
  type: string
  animated: boolean
  showArrow: boolean
  style: {
    strokeWidth: number
    stroke: string
  }
  markerEnd: {
    type: any
    color: string
    width: number
    height: number
    strokeWidth: number
    markerUnits: string
  }
}

/**
 * 吸附网格配置
 */
export interface SnapGrid {
  x: number
  y: number
}

/**
 * 默认视口配置
 */
export interface DefaultViewport {
  x: number
  y: number
  zoom: number
}

/**
 * 小地图配置
 */
export interface MiniMapConfig {
  backgroundColor: string
  nodeStrokeColor: string
  nodeStrokeWidth: number
}

/**
 * 背景配置
 */
export interface BackgroundConfig {
  patternColor: string
  gap: number
  size: number
  variant: string
}

/**
 * 动画配置
 */
export interface AnimationConfig {
  enableDragAnimation: boolean
  enableAINodeAnimation: boolean
  enableNodeHighlight: boolean
}

/**
 * 流程设置状态接口
 */
export interface FlowSettingsState {
  // 执行设置
  executionMode: FlowExecutionMode
  maxConcurrentNodes: number
  executionTimeout: number // 秒

  // 编辑器设置
  connectionMode: NodeConnectionMode
  validationLevel: FlowValidationLevel
  layoutDirection: FlowLayoutDirection

  // 网格设置
  grid: GridSettings

  // 缩放设置
  zoom: ZoomSettings

  // 自动保存设置
  autoSave: AutoSaveSettings

  // 性能设置
  performance: PerformanceSettings

  // 调试设置
  debug: DebugSettings

  // 边缘配置
  edgeConfig: EdgeConfig

  // 吸附设置
  snapToLines: boolean
  snapToGrid: boolean
  snapGrid: SnapGrid

  // 视口设置
  minZoom: number
  maxZoom: number
  fitViewOnInit: boolean
  defaultViewport: DefaultViewport

  // 界面控制
  showMiniMap: boolean
  showLeftControls: boolean
  showRightControls: boolean

  // 小地图配置
  miniMapConfig: MiniMapConfig

  // 背景配置
  backgroundConfig: BackgroundConfig

  // 动画配置
  animationConfig: AnimationConfig
}

/**
 * 默认流程设置
 */
const defaultFlowSettings: FlowSettingsState = {
  // 执行设置
  executionMode: 'sequential',
  maxConcurrentNodes: 5,
  executionTimeout: 300, // 5分钟

  // 编辑器设置
  connectionMode: 'auto',
  validationLevel: 'basic',
  layoutDirection: 'horizontal',

  // 网格设置
  grid: {
    enabled: true,
    size: 20,
    snapToGrid: true,
    showGrid: true,
  },

  // 缩放设置
  zoom: {
    minZoom: 0.1,
    maxZoom: 2.0,
    defaultZoom: 1.0,
    zoomStep: 0.1,
  },

  // 自动保存设置
  autoSave: {
    enabled: true,
    interval: 30, // 30秒
    maxBackups: 10,
  },

  // 性能设置
  performance: {
    enableVirtualization: true,
    maxVisibleNodes: 100,
    enableLazyLoading: true,
    renderThrottleMs: 16, // 60fps
  },

  // 调试设置
  debug: {
    showNodeIds: false,
    showExecutionPath: false,
    enablePerformanceMonitor: false,
    logLevel: 'warn',
  },

  // 边缘配置
  edgeConfig: {
    type: 'smoothstep',
    animated: false,
    showArrow: true,
    style: {
      strokeWidth: 2,
      stroke: '#b1b1b7',
    },
    markerEnd: {
      type: 'arrowclosed',
      color: '#b1b1b7',
      width: 20,
      height: 20,
      strokeWidth: 2,
      markerUnits: 'strokeWidth',
    },
  },

  // 吸附设置
  snapToLines: true,
  snapToGrid: true,
  snapGrid: {
    x: 20,
    y: 20,
  },

  // 视口设置
  minZoom: 0.1,
  maxZoom: 2.0,
  fitViewOnInit: true,
  defaultViewport: {
    x: 0,
    y: 0,
    zoom: 1.0,
  },

  // 界面控制
  showMiniMap: true,
  showLeftControls: true,
  showRightControls: true,

  // 小地图配置
  miniMapConfig: {
    backgroundColor: '#f8f9fa',
    nodeStrokeColor: '#374151',
    nodeStrokeWidth: 1,
  },

  // 背景配置
  backgroundConfig: {
    patternColor: '#e5e7eb',
    gap: 20,
    size: 1,
    variant: 'dots',
  },

  // 动画配置
  animationConfig: {
    enableDragAnimation: true,
    enableAINodeAnimation: true,
    enableNodeHighlight: true,
  },
}

/**
 * 流程设置状态管理
 */
export const useFlowSettingsStore = defineStore(
  'flow-settings',
  () => {
    // 状态
    const settings = ref<FlowSettingsState>({ ...defaultFlowSettings })

    // 计算属性
    const isHighPerformanceMode = computed(
      () =>
        settings.value.performance.enableVirtualization &&
        settings.value.performance.enableLazyLoading
    )

    const isDebugMode = computed(
      () =>
        settings.value.debug.logLevel !== 'none' ||
        settings.value.debug.showNodeIds ||
        settings.value.debug.showExecutionPath
    )

    // 方法
    const updateSettings = (updates: Partial<FlowSettingsState>) => {
      settings.value = { ...settings.value, ...updates }
    }

    const updateGridSettings = (updates: Partial<GridSettings>) => {
      settings.value.grid = { ...settings.value.grid, ...updates }
    }

    const updateZoomSettings = (updates: Partial<ZoomSettings>) => {
      settings.value.zoom = { ...settings.value.zoom, ...updates }
    }

    const updateAutoSaveSettings = (updates: Partial<AutoSaveSettings>) => {
      settings.value.autoSave = { ...settings.value.autoSave, ...updates }
    }

    const updatePerformanceSettings = (updates: Partial<PerformanceSettings>) => {
      settings.value.performance = { ...settings.value.performance, ...updates }
    }

    const updateDebugSettings = (updates: Partial<DebugSettings>) => {
      settings.value.debug = { ...settings.value.debug, ...updates }
    }

    const updateEdgeConfig = (updates: Partial<EdgeConfig>) => {
      settings.value.edgeConfig = { ...settings.value.edgeConfig, ...updates }
    }

    const updateEdgeStyle = (updates: Partial<EdgeConfig['style']>) => {
      settings.value.edgeConfig.style = { ...settings.value.edgeConfig.style, ...updates }
    }

    const updateEdgeMarkerEnd = (updates: Partial<EdgeConfig['markerEnd']>) => {
      settings.value.edgeConfig.markerEnd = { ...settings.value.edgeConfig.markerEnd, ...updates }
    }

    const updateSnapGrid = (updates: Partial<SnapGrid>) => {
      settings.value.snapGrid = { ...settings.value.snapGrid, ...updates }
    }

    const updateDefaultViewport = (updates: Partial<DefaultViewport>) => {
      settings.value.defaultViewport = { ...settings.value.defaultViewport, ...updates }
    }

    const updateMiniMapConfig = (updates: Partial<MiniMapConfig>) => {
      settings.value.miniMapConfig = { ...settings.value.miniMapConfig, ...updates }
    }

    const updateBackgroundConfig = (updates: Partial<BackgroundConfig>) => {
      settings.value.backgroundConfig = { ...settings.value.backgroundConfig, ...updates }
    }

    const updateAnimationConfig = (updates: Partial<AnimationConfig>) => {
      settings.value.animationConfig = { ...settings.value.animationConfig, ...updates }
    }

    const resetToDefaults = () => {
      settings.value = { ...defaultFlowSettings }
    }

    const exportSettings = () => {
      return {
        ...settings.value,
        exportTime: new Date().toISOString(),
      }
    }

    const importSettings = (importedSettings: Partial<FlowSettingsState>) => {
      updateSettings(importedSettings)
    }

    return {
      // 状态
      settings,

      // 计算属性
      isHighPerformanceMode,
      isDebugMode,

      // 方法
      updateSettings,
      updateGridSettings,
      updateZoomSettings,
      updateAutoSaveSettings,
      updatePerformanceSettings,
      updateDebugSettings,
      updateEdgeConfig,
      updateEdgeStyle,
      updateEdgeMarkerEnd,
      updateSnapGrid,
      updateDefaultViewport,
      updateMiniMapConfig,
      updateBackgroundConfig,
      updateAnimationConfig,
      resetToDefaults,
      exportSettings,
      importSettings,
    }
  },
  {
    persist: {
      key: 'mattverse-flow-settings',
      storage: localStorage,
    },
  }
)
