<template>
  <div id="app" class="w-full min-h-full flex flex-col">
    <!-- Electron 标题栏 -->
    <TitleBar
      title="MattVerse 电池设计自动化平台"
      :show-right-icon="true"
      :right-icon-src="mattverseIcon"
    />

    <!-- 主要内容区域 -->
    <div class="flex-1">
      <router-view />
    </div>

    <!-- Toast 通知 -->
    <Toaster close-button rich-colors position="bottom-right" theme="light" />
  </div>
</template>

<script setup lang="ts">
import { Toaster, TitleBar } from '@mattverse/mattverse-ui'
// 导入MattVerse图标
import mattverseIcon from '@mattverse/shared/src/assets/images/icons/mattverse/mattverse.png'
</script>
