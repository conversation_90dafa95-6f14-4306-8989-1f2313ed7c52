<template>
  <div id="app" class="w-full min-h-full">
    <!-- Electron 标题栏 -->
    <TitleBar
      title="MattVerse 电池设计自动化平台"
      :show-right-icon="true"
      :right-icon-src="mattverseIcon"
    />

    <!-- 主要内容区域 -->
    <div class="pt-8">
      <router-view />
    </div>

    <!-- Toast 通知 -->
    <Toaster close-button rich-colors position="bottom-right" theme="light" />
  </div>
</template>

<script setup lang="ts">
import { Toaster, TitleBar } from '@mattverse/mattverse-ui'

// 使用public目录中的图标路径，使用ico格式
const mattverseIcon = '/images/icons/mattverse/mattverse.ico'
</script>
