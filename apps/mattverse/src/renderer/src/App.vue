<template>
  <div id="app" class="w-full min-h-full">
    <!-- Electron 标题栏 -->
    <TitleBar
      title="MattVerse 电池设计自动化平台"
      :show-right-icon="true"
      :right-icon-src="mattverseIcon"
      :platform="currentPlatform"
    />

    <!-- 主要内容区域 -->
    <div class="pt-8">
      <router-view />
    </div>

    <!-- Toast 通知 -->
    <Toaster close-button rich-colors position="bottom-right" theme="light" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Toaster, TitleBar } from '@mattverse/mattverse-ui'

// 使用public目录中的图标路径，使用ico格式
const mattverseIcon = '/images/icons/mattverse/mattverse.ico'

// 检测当前平台
const currentPlatform = computed(() => {
  // 在Electron环境中，可以通过electronAPI获取平台信息
  if (typeof window !== 'undefined' && window.electronAPI) {
    // 这里可以通过electronAPI获取平台信息，暂时使用navigator检测
    const userAgent = navigator.userAgent.toLowerCase()
    if (userAgent.includes('mac')) {
      return 'darwin'
    } else if (userAgent.includes('win')) {
      return 'win32'
    } else {
      return 'linux'
    }
  }
  // 默认返回当前系统平台
  return 'win32'
})
</script>
